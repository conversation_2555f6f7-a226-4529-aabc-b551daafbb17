// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de Sígilos Auracron Bridge Implementation

#include "AuracronSigilosBridge.h"
#include "AuracronSigilGameplayTags.h"
#include "AuracronSigilEffects.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "GameFramework/GameStateBase.h"
#include "GameFramework/PlayerState.h"
#include "GameFramework/Pawn.h"
#include "Components/TimelineComponent.h"
#include "AbilitySystemComponent.h"
#include "AbilitySystemBlueprintLibrary.h"
#include "GameplayAbilitySpec.h"
#include "GameplayTagsManager.h"
#include "NiagaraComponent.h"
#include "NiagaraFunctionLibrary.h"
#include "Components/AudioComponent.h"
#include "Kismet/GameplayStatics.h"
#include "InputActionValue.h"
#include "Engine/StreamableManager.h"
#include "Engine/AssetManager.h"
#include "AuracronCombatInterface.h"
#include "AuracronProgressionInterface.h"
#include "TimerManager.h"
#include "GameFramework/ProjectileMovementComponent.h"
#include "Engine/OverlapResult.h"
#include "Net/UnrealNetwork.h"
#include "Engine/ActorChannel.h"
#include "DrawDebugHelpers.h"
#include "Async/Async.h"
#include "HAL/ThreadSafeBool.h"
#include "GameplayEffectExtension.h"
#include "AttributeSet.h"
#include "Perception/AIPerceptionComponent.h"
#include "Perception/AISenseConfig_Sight.h"
#include "Abilities/GameplayAbilityTargetTypes.h"
#include "MetasoundSource.h"
#include "MetaSoundSource.h"
#include "Camera/CameraComponent.h"
#include "EnhancedInputComponent.h"

UAuracronSigilosBridge::UAuracronSigilosBridge()
{
    PrimaryComponentTick.bCanEverTick = true;
    PrimaryComponentTick.TickInterval = 0.1f; // 10 FPS para otimização

    // Configurar replicação
    SetIsReplicatedByDefault(true);

    // Criar componente de timeline
    FusionTimeline = CreateDefaultSubobject<UTimelineComponent>(TEXT("FusionTimeline"));

    // Inicializar Sígilos equipados
    EquippedAegisSigil.MainType = EAuracronSigiloType::Aegis;
    EquippedAegisSigil.AegisSubtype = EAuracronAegisSigilType::None;

    EquippedRuinSigil.MainType = EAuracronSigiloType::Ruin;
    EquippedRuinSigil.RuinSubtype = EAuracronRuinSigilType::None;

    EquippedVesperSigil.MainType = EAuracronSigiloType::Vesper;
    EquippedVesperSigil.VesperSubtype = EAuracronVesperSigilType::None;

    // Inicializar configurações padrão
    LoadDefaultSigiloConfigurations();
}

void UAuracronSigilosBridge::BeginPlay()
{
    Super::BeginPlay();

    UE_LOG(LogTemp, Warning, TEXT("AURACRON: Inicializando Sistema de Sígilos"));

    // Obter referência ao AbilitySystemComponent
    if (AActor* Owner = GetOwner())
    {
        AbilitySystemComponent = UAbilitySystemBlueprintLibrary::GetAbilitySystemComponent(Owner);
        if (!AbilitySystemComponent)
        {
            UE_LOG(LogTemp, Error, TEXT("AURACRON: AbilitySystemComponent não encontrado no Owner"));
            return;
        }
    }

    // Inicializar sistema
    bSystemInitialized = InitializeSigiloSystem();
    
    if (bSystemInitialized)
    {
        // Configurar timer para verificações periódicas
        GetWorld()->GetTimerManager().SetTimer(
            SystemUpdateTimer,
            [this]()
            {
                // Verificar se fusão está disponível (após 6 minutos)
                if (!bFusionAvailable && GetWorld()->GetTimeSeconds() >= 360.0f)
                {
                    bFusionAvailable = true;
                    UE_LOG(LogTemp, Log, TEXT("AURACRON: Fusão de Sigilo agora disponível"));
                }
            },
            1.0f, // A cada segundo
            true  // Loop
        );
        
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Sistema de Sígilos inicializado com sucesso"));
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao inicializar Sistema de Sígilos"));
    }
}

void UAuracronSigilosBridge::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // Limpar timers
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().ClearTimer(SystemUpdateTimer);
    }
    
    // Remover efeitos ativos
    RemoveSigiloGameplayEffects();
    
    // Limpar componentes visuais
    for (UNiagaraComponent* Component : ActiveVisualEffects)
    {
        if (IsValid(Component))
        {
            Component->DestroyComponent();
        }
    }
    ActiveVisualEffects.Empty();
    
    // Limpar componentes de áudio
    for (UAudioComponent* Component : ActiveAudioComponents)
    {
        if (IsValid(Component))
        {
            Component->Stop();
            Component->DestroyComponent();
        }
    }
    ActiveAudioComponents.Empty();

    Super::EndPlay(EndPlayReason);
}

void UAuracronSigilosBridge::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    DOREPLIFETIME(UAuracronSigilosBridge, SigiloConfigurations);
    DOREPLIFETIME(UAuracronSigilosBridge, SelectedSigiloType);
    DOREPLIFETIME(UAuracronSigilosBridge, CurrentFusionState);
    DOREPLIFETIME(UAuracronSigilosBridge, FusionStartTime);
    DOREPLIFETIME(UAuracronSigilosBridge, LastReforgeTime);

    // Fusion 2.0 replication
    DOREPLIFETIME(UAuracronSigilosBridge, EquippedAegisSigil);
    DOREPLIFETIME(UAuracronSigilosBridge, EquippedRuinSigil);
    DOREPLIFETIME(UAuracronSigilosBridge, EquippedVesperSigil);
    DOREPLIFETIME(UAuracronSigilosBridge, CurrentArchetype);
    DOREPLIFETIME(UAuracronSigilosBridge, bFusion20Active);
    DOREPLIFETIME(UAuracronSigilosBridge, Fusion20StartTime);
    DOREPLIFETIME(UAuracronSigilosBridge, LastFusion20Time);

    // Replicate compressed network data using UE 5.6 efficient replication
    DOREPLIFETIME(UAuracronSigilosBridge, CompressedSigilState);
    DOREPLIFETIME(UAuracronSigilosBridge, CompressedAegisCooldown);
    DOREPLIFETIME(UAuracronSigilosBridge, CompressedRuinCooldown);
    DOREPLIFETIME(UAuracronSigilosBridge, CompressedVesperCooldown);
    DOREPLIFETIME(UAuracronSigilosBridge, CompressedFusion20Cooldown);
}

void UAuracronSigilosBridge::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
    Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

    if (!bSystemInitialized)
        return;

    // Processar fusão em andamento
    if (CurrentFusionState == EAuracronSigiloFusionState::Charging)
    {
        ProcessFusion(DeltaTime);
    }

    // Processar cooldown de re-forjamento
    if (LastReforgeTime > 0.0f)
    {
        ProcessReforgeCooldown(DeltaTime);
    }

    // Update Fusion 2.0 duration if active (UE 5.6 enhanced)
    if (bFusion20Active && Fusion20Duration > 0.0f)
    {
        Fusion20Duration -= DeltaTime;
        if (Fusion20Duration <= 0.0f)
        {
            EndFusion20();
        }
    }

    // Update individual sigil cooldowns with dynamic balance
    UpdateSigilCooldownsWithBalance(DeltaTime);

    // Compress replication data for network optimization (UE 5.6)
    if (GetOwner() && GetOwner()->HasAuthority())
    {
        CompressReplicationData();
    }
    else
    {
        DecompressReplicationData();
    }

    // Update mastery system periodically
    static float MasteryUpdateTimer = 0.0f;
    MasteryUpdateTimer += DeltaTime;
    if (MasteryUpdateTimer >= 5.0f) // Update every 5 seconds
    {
        UpdateSigilMastery();
        MasteryUpdateTimer = 0.0f;
    }

    // Optimize network replication periodically
    static float NetworkOptimizationTimer = 0.0f;
    NetworkOptimizationTimer += DeltaTime;
    if (NetworkOptimizationTimer >= 1.0f) // Optimize every second
    {
        OptimizeNetworkReplication();
        NetworkOptimizationTimer = 0.0f;
    }

    // Update performance metrics if monitoring is active
    if (PerformanceMonitoringActive)
    {
        PerformanceMetrics.SigilActivationCount = AegisActivationCount + RuinActivationCount + VesperActivationCount;
        PerformanceMetrics.ArchetypeGenerationCount++; // Increment for tracking
    }
}

// === Core Sigilo Management ===

bool UAuracronSigilosBridge::SelectSigilo(EAuracronSigiloType SigiloType)
{
    if (!bSystemInitialized)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Sistema não inicializado"));
        return false;
    }

    if (SigiloType == EAuracronSigiloType::None)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Tentativa de selecionar Sigilo None"));
        return false;
    }

    if (!ValidateSigiloSelection(SigiloType))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Seleção de Sigilo inválida: %d"), (int32)SigiloType);
        return false;
    }

    // Verificar se já há um Sigilo selecionado
    if (SelectedSigiloType != EAuracronSigiloType::None && CurrentFusionState != EAuracronSigiloFusionState::Inactive)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Já existe um Sigilo ativo"));
        return false;
    }

    SelectedSigiloType = SigiloType;
    CurrentFusionState = EAuracronSigiloFusionState::Inactive;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Sigilo selecionado: %d"), (int32)SigiloType);

    // Broadcast evento
    OnSigiloSelected.Broadcast(SigiloType);

    return true;
}

bool UAuracronSigilosBridge::StartSigiloFusion()
{
    if (!bSystemInitialized || !AbilitySystemComponent)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Sistema não inicializado ou ASC inválido"));
        return false;
    }

    if (SelectedSigiloType == EAuracronSigiloType::None)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Nenhum Sigilo selecionado"));
        return false;
    }

    if (!bFusionAvailable)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Fusão ainda não disponível (6 minutos não passaram)"));
        return false;
    }

    if (CurrentFusionState != EAuracronSigiloFusionState::Inactive)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Fusão já em andamento ou ativa"));
        return false;
    }

    CurrentFusionState = EAuracronSigiloFusionState::Charging;
    FusionStartTime = GetWorld()->GetTimeSeconds();

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Iniciando fusão do Sigilo: %d"), (int32)SelectedSigiloType);

    // Obter configuração do Sigilo
    const FAuracronSigiloConfigurationEntry* ConfigEntry = SigiloConfigurations.FindByPredicate([&](const FAuracronSigiloConfigurationEntry& Entry) {
        return Entry.SigiloType == SelectedSigiloType;
    });

    if (ConfigEntry)
    {
        // Iniciar efeitos visuais de fusão
        UpdateVisualEffects(ConfigEntry->Configuration.VisualEffects);
        
        // Reproduzir som de fusão
        PlayAudioEffects(ConfigEntry->Configuration.AudioEffects);
    }

    // Broadcast evento
    OnFusionStarted.Broadcast();

    return true;
}

bool UAuracronSigilosBridge::CompleteSigiloFusion()
{
    if (!bSystemInitialized || !AbilitySystemComponent)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Sistema não inicializado ou ASC inválido"));
        return false;
    }

    if (CurrentFusionState != EAuracronSigiloFusionState::Charging)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Fusão não está em andamento"));
        return false;
    }

    CurrentFusionState = EAuracronSigiloFusionState::Active;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Completando fusão do Sigilo: %d"), (int32)SelectedSigiloType);

    // Obter configuração do Sigilo
    const FAuracronSigiloConfigurationEntry* ConfigEntry = SigiloConfigurations.FindByPredicate([&](const FAuracronSigiloConfigurationEntry& Entry) {
        return Entry.SigiloType == SelectedSigiloType;
    });

    if (ConfigEntry)
    {
        // Aplicar GameplayEffects
        ApplySigiloGameplayEffects(ConfigEntry->Configuration);
        
        // Aplicar bônus passivos
        ApplyPassiveBonuses();
        
        // Atualizar efeitos visuais para estado ativo
        UpdateVisualEffects(ConfigEntry->Configuration.VisualEffects);
    }

    // Broadcast evento
    OnFusionCompleted.Broadcast(SelectedSigiloType);

    return true;
}

bool UAuracronSigilosBridge::ReforgeSigilo(EAuracronSigiloType NewSigiloType)
{
    if (!bSystemInitialized || !AbilitySystemComponent)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Sistema não inicializado ou ASC inválido"));
        return false;
    }

    if (!CanReforge())
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Re-forjamento não disponível (cooldown ativo)"));
        return false;
    }

    if (NewSigiloType == EAuracronSigiloType::None || NewSigiloType == SelectedSigiloType)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Tipo de Sigilo inválido para re-forjamento"));
        return false;
    }

    if (!ValidateSigiloSelection(NewSigiloType))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Seleção de novo Sigilo inválida: %d"), (int32)NewSigiloType);
        return false;
    }

    // Implementação completa do re-forjamento usando UE5.6 APIs <mcreference link="https://dev.epicgames.com/documentation/en-us/unreal-engine/gameplay-ability-system-for-unreal-engine" index="1">1</mcreference>
    EAuracronSigiloType OldSigiloType = SelectedSigiloType;
    
    // Remover efeitos do Sigilo atual
    if (CurrentFusionState == EAuracronSigiloFusionState::Active)
    {
        RemoveSigiloGameplayEffects();
        RemovePassiveBonuses();
    }
    
    // Atualizar para o novo Sigilo
    SelectedSigiloType = NewSigiloType;
    CurrentFusionState = EAuracronSigiloFusionState::Reforging;
    LastReforgeTime = GetWorld()->GetTimeSeconds();
    
    // Aplicar configuração do novo Sigilo
    const FAuracronSigiloConfiguration* NewConfig = nullptr;
    for (const FAuracronSigiloConfigurationEntry& Entry : SigiloConfigurations)
    {
        if (Entry.SigiloType == NewSigiloType)
        {
            NewConfig = &Entry.Configuration;
            break;
        }
    }
    
    if (NewConfig)
    {
        // Aplicar novos efeitos visuais e sonoros
        UpdateVisualEffects(NewConfig->VisualEffects);
        PlayAudioEffects(NewConfig->AudioEffects);
        
        // Se estava ativo, aplicar novos efeitos de gameplay
        if (CurrentFusionState == EAuracronSigiloFusionState::Reforging)
        {
            ApplySigiloGameplayEffects(*NewConfig);
            ApplyPassiveBonuses();
            CurrentFusionState = EAuracronSigiloFusionState::Active;
        }
        else
        {
            CurrentFusionState = EAuracronSigiloFusionState::Inactive;
        }
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Sigilo re-forjado de %d para %d"), (int32)OldSigiloType, (int32)NewSigiloType);
    
    // Broadcast evento
    OnSigiloReforged.Broadcast(OldSigiloType, NewSigiloType);

    return true;
}

bool UAuracronSigilosBridge::CancelSigiloFusion()
{
    if (CurrentFusionState != EAuracronSigiloFusionState::Charging)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Nenhuma fusão em andamento para cancelar"));
        return false;
    }

    CurrentFusionState = EAuracronSigiloFusionState::Inactive;
    FusionStartTime = 0.0f;

    // Limpar efeitos visuais
    for (UNiagaraComponent* Component : ActiveVisualEffects)
    {
        if (IsValid(Component))
        {
            Component->Deactivate();
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Fusão cancelada"));
    return true;
}

// === Ability Management ===

bool UAuracronSigilosBridge::ActivateExclusiveAbility()
{
    if (!bSystemInitialized || !AbilitySystemComponent)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Sistema não inicializado ou ASC inválido"));
        return false;
    }

    if (CurrentFusionState != EAuracronSigiloFusionState::Active)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Sigilo não está ativo"));
        return false;
    }

    const FAuracronSigiloConfigurationEntry* ConfigEntry = SigiloConfigurations.FindByPredicate([&](const FAuracronSigiloConfigurationEntry& Entry) {
        return Entry.SigiloType == SelectedSigiloType;
    });

    if (!ConfigEntry || !ConfigEntry->Configuration.ExclusiveAbility.AbilityClass.IsValid())
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Habilidade exclusiva não configurada"));
        return false;
    }

    // Tentar ativar a habilidade exclusiva
    TSubclassOf<UGameplayAbility> AbilityClass = ConfigEntry->Configuration.ExclusiveAbility.AbilityClass.LoadSynchronous();
    if (AbilityClass)
    {
        bool bActivated = AbilitySystemComponent->TryActivateAbilityByClass(AbilityClass);
        if (bActivated)
        {
            UE_LOG(LogTemp, Log, TEXT("AURACRON: Habilidade exclusiva ativada: %s"), *AbilityClass->GetName());
            return true;
        }
    }

    UE_LOG(LogTemp, Warning, TEXT("AURACRON: Falha ao ativar habilidade exclusiva"));
    return false;
}

TArray<TSubclassOf<UGameplayAbility>> UAuracronSigilosBridge::GetAlternativeAbilityTree() const
{
    TArray<TSubclassOf<UGameplayAbility>> AlternativeAbilities;

    if (CurrentFusionState != EAuracronSigiloFusionState::Active)
    {
        return AlternativeAbilities;
    }

    const FAuracronSigiloConfigurationEntry* ConfigEntry = SigiloConfigurations.FindByPredicate([&](const FAuracronSigiloConfigurationEntry& Entry) {
        return Entry.SigiloType == SelectedSigiloType;
    });

    if (ConfigEntry && ConfigEntry->Configuration.ExclusiveAbility.AbilityClass.IsValid())
    {
        TSubclassOf<UGameplayAbility> AbilityClass = ConfigEntry->Configuration.ExclusiveAbility.AbilityClass.LoadSynchronous();
        if (AbilityClass)
        {
            AlternativeAbilities.Add(AbilityClass);
        }
    }

    return AlternativeAbilities;
}

bool UAuracronSigilosBridge::ApplyPassiveBonuses()
{
    if (!bSystemInitialized || !AbilitySystemComponent)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Sistema não inicializado ou ASC inválido"));
        return false;
    }

    const FAuracronSigiloConfigurationEntry* ConfigEntry = SigiloConfigurations.FindByPredicate([&](const FAuracronSigiloConfigurationEntry& Entry) {
        return Entry.SigiloType == SelectedSigiloType;
    });

    if (!ConfigEntry)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Configuração do Sigilo não encontrada"));
        return false;
    }

    const FAuracronSigiloConfiguration* Config = &ConfigEntry->Configuration;


    // Aplicar GameplayEffects dos bônus passivos
    for (const TSoftClassPtr<UGameplayEffect>& EffectClass : Config->FusionGameplayEffects)
    {
        if (EffectClass.IsValid())
        {
            TSubclassOf<UGameplayEffect> LoadedEffect = EffectClass.LoadSynchronous();
            if (LoadedEffect)
            {
                FGameplayEffectContextHandle EffectContext = AbilitySystemComponent->MakeEffectContext();
                FGameplayEffectSpecHandle SpecHandle = AbilitySystemComponent->MakeOutgoingSpec(LoadedEffect, 1.0f, EffectContext);

                if (SpecHandle.IsValid())
                {
                    FActiveGameplayEffectHandle ActiveHandle = AbilitySystemComponent->ApplyGameplayEffectSpecToSelf(*SpecHandle.Data.Get());
                    if (ActiveHandle.IsValid())
                    {
                        ActivePassiveEffects.Add(ActiveHandle);
                        UE_LOG(LogTemp, Log, TEXT("AURACRON: Bônus passivo aplicado: %s"), *LoadedEffect->GetName());
                    }
                }
            }
        }
    }

    // Aplicar tags de fusão
    if (Config->FusionTags.Num() > 0)
    {
        AbilitySystemComponent->AddLooseGameplayTags(Config->FusionTags);
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Bônus passivos aplicados para Sigilo %d"), (int32)SelectedSigiloType);
    return true;
}

bool UAuracronSigilosBridge::RemovePassiveBonuses()
{
    if (!bSystemInitialized || !AbilitySystemComponent)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Sistema não inicializado ou ASC inválido"));
        return false;
    }

    // Remover todos os efeitos passivos ativos usando UE5.6 API
    int32 RemovedEffects = 0;
    for (const FActiveGameplayEffectHandle& Handle : ActivePassiveEffects)
    {
        if (Handle.IsValid())
        {
            bool bRemoved = AbilitySystemComponent->RemoveActiveGameplayEffect(Handle);
            if (bRemoved)
            {
                RemovedEffects++;
                UE_LOG(LogTemp, Verbose, TEXT("AURACRON: Efeito passivo removido com sucesso"));
            }
        }
    }
    ActivePassiveEffects.Empty();

    // Remover GameplayEffects ativos do Sigilo
    for (const FActiveGameplayEffectHandle& Handle : ActiveSigiloEffects)
    {
        if (Handle.IsValid())
        {
            AbilitySystemComponent->RemoveActiveGameplayEffect(Handle);
        }
    }
    ActiveSigiloEffects.Empty();

    // Remover tags de fusão
    const FAuracronSigiloConfigurationEntry* ConfigEntry = SigiloConfigurations.FindByPredicate([&](const FAuracronSigiloConfigurationEntry& Entry) {
        return Entry.SigiloType == SelectedSigiloType;
    });

    if (ConfigEntry && ConfigEntry->Configuration.FusionTags.Num() > 0)
    {
        AbilitySystemComponent->RemoveLooseGameplayTags(ConfigEntry->Configuration.FusionTags);
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: %d bônus passivos removidos"), RemovedEffects);
    return true;
}

// === State Management ===

float UAuracronSigilosBridge::GetTimeToFusion() const
{
    if (bFusionAvailable)
    {
        return 0.0f;
    }

    float GameTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;
    return FMath::Max(0.0f, 360.0f - GameTime); // 6 minutos = 360 segundos
}

float UAuracronSigilosBridge::GetReforgeCooldownRemaining() const
{
    if (LastReforgeTime < 0.0f)
    {
        return 0.0f;
    }

    const FAuracronSigiloConfigurationEntry* ConfigEntry = SigiloConfigurations.FindByPredicate([&](const FAuracronSigiloConfigurationEntry& Entry) {
        return Entry.SigiloType == SelectedSigiloType;
    });

    if (!ConfigEntry)
    {
        return 0.0f;
    }

    const FAuracronSigiloConfiguration* Config = &ConfigEntry->Configuration;

    float GameTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;
    float CooldownDuration = Config->Properties.ReforgeCooldown;
    float ElapsedTime = GameTime - LastReforgeTime;

    return FMath::Max(0.0f, CooldownDuration - ElapsedTime);
}

bool UAuracronSigilosBridge::CanReforge() const
{
     // Verificar se o sistema está inicializado
     if (!bSystemInitialized)
     {
         return false;
     }
     
     // Verificar se há um Sigilo selecionado
     if (SelectedSigiloType == EAuracronSigiloType::None)
     {
         return false;
     }
     
     // Verificar se não está em cooldown de re-forjamento
     float CurrentTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;
     const FAuracronSigiloConfigurationEntry* ConfigEntry = SigiloConfigurations.FindByPredicate([&](const FAuracronSigiloConfigurationEntry& Entry) {
         return Entry.SigiloType == SelectedSigiloType;
     });

     if (ConfigEntry && LastReforgeTime > 0.0f)
     {
         float TimeSinceLastReforge = CurrentTime - LastReforgeTime;
         float CooldownDuration = ConfigEntry->Configuration.Properties.ReforgeCooldown;
         
         if (TimeSinceLastReforge < CooldownDuration)
         {
             return false; // Ainda em cooldown
         }
     }
     
     // Verificar se não está em processo de fusão
     if (CurrentFusionState == EAuracronSigiloFusionState::Charging)
     {
         return false;
     }
     
     return true;
}

// === Configuration Management ===

FAuracronSigiloConfiguration UAuracronSigilosBridge::GetSigiloConfiguration(EAuracronSigiloType SigiloType) const
{
    const FAuracronSigiloConfigurationEntry* ConfigEntry = SigiloConfigurations.FindByPredicate([&](const FAuracronSigiloConfigurationEntry& Entry) {
        return Entry.SigiloType == SigiloType;
    });

    if (ConfigEntry)
    {
        return ConfigEntry->Configuration;
    }

    return FAuracronSigiloConfiguration();
}

void UAuracronSigilosBridge::SetSigiloConfiguration(EAuracronSigiloType SigiloType, const FAuracronSigiloConfiguration& Configuration)
{
    // Procurar se já existe uma entrada para este tipo de sigilo
    FAuracronSigiloConfigurationEntry* ExistingEntry = SigiloConfigurations.FindByPredicate([SigiloType](const FAuracronSigiloConfigurationEntry& Entry)
    {
        return Entry.SigiloType == SigiloType;
    });

    if (ExistingEntry)
    {
        // Atualizar configuração existente
        ExistingEntry->Configuration = Configuration;
    }
    else
    {
        // Criar nova entrada
        FAuracronSigiloConfigurationEntry NewEntry;
        NewEntry.SigiloType = SigiloType;
        NewEntry.Configuration = Configuration;
        SigiloConfigurations.Add(NewEntry);
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Configuração do Sigilo %d atualizada"), (int32)SigiloType);
}

bool UAuracronSigilosBridge::LoadDefaultSigiloConfigurations()
{
    // Configuração padrão para Aegis (Tank)
    FAuracronSigiloConfiguration AegisConfig;
    AegisConfig.Properties.SigiloType = EAuracronSigiloType::Aegis;
    AegisConfig.Properties.SigiloName = FText::FromString(TEXT("Aegis"));
    AegisConfig.Properties.SigiloDescription = FText::FromString(TEXT("Sigilo de Tanque - Aumenta HP e Armadura"));
    AegisConfig.Properties.PrimaryColor = FLinearColor::Blue;
    AegisConfig.Properties.SecondaryColor = FLinearColor(0.75f, 0.75f, 0.75f, 1.0f); // Silver color

    AegisConfig.PassiveBonuses.HealthBonus = 0.15f; // +15% HP
    AegisConfig.PassiveBonuses.ArmorBonus = 0.15f; // +15% Armadura
    AegisConfig.PassiveBonuses.DamageResistance = 0.05f; // +5% Resistência

    AegisConfig.ExclusiveAbility.AbilityName = FText::FromString(TEXT("Murallion"));
    AegisConfig.ExclusiveAbility.AbilityDescription = FText::FromString(TEXT("Cria barreira circular por 3 segundos"));
    AegisConfig.ExclusiveAbility.Cooldown = 45.0f;
    AegisConfig.ExclusiveAbility.ManaCost = 80.0f;
    AegisConfig.ExclusiveAbility.Duration = 3.0f;
    AegisConfig.ExclusiveAbility.Range = 400.0f;

    // Configuração padrão para Ruin (Damage)
    FAuracronSigiloConfiguration RuinConfig;
    RuinConfig.Properties.SigiloType = EAuracronSigiloType::Ruin;
    RuinConfig.Properties.SigiloName = FText::FromString(TEXT("Ruin"));
    RuinConfig.Properties.SigiloDescription = FText::FromString(TEXT("Sigilo de Dano - Aumenta ATK/AP"));
    RuinConfig.Properties.PrimaryColor = FLinearColor::Red;
    RuinConfig.Properties.SecondaryColor = FLinearColor::Black;

    RuinConfig.PassiveBonuses.AttackBonus = 0.12f; // +12% ATK
    RuinConfig.PassiveBonuses.AbilityPowerBonus = 0.12f; // +12% AP
    RuinConfig.PassiveBonuses.CooldownReduction = 0.08f; // +8% CDR

    RuinConfig.ExclusiveAbility.AbilityName = FText::FromString(TEXT("Fracasso Prismal"));
    RuinConfig.ExclusiveAbility.AbilityDescription = FText::FromString(TEXT("Reset parcial de cooldowns"));
    RuinConfig.ExclusiveAbility.Cooldown = 60.0f;
    RuinConfig.ExclusiveAbility.ManaCost = 100.0f;
    RuinConfig.ExclusiveAbility.Duration = 1.0f;

    // Configuração padrão para Vesper (Utility)
    FAuracronSigiloConfiguration VesperConfig;
    VesperConfig.Properties.SigiloType = EAuracronSigiloType::Vesper;
    VesperConfig.Properties.SigiloName = FText::FromString(TEXT("Vesper"));
    VesperConfig.Properties.SigiloDescription = FText::FromString(TEXT("Sigilo de Utilidade - Aumenta Velocidade e CDR"));
    VesperConfig.Properties.PrimaryColor = FLinearColor::Green;
    VesperConfig.Properties.SecondaryColor = FLinearColor::Yellow;

    VesperConfig.PassiveBonuses.MovementSpeedBonus = 0.10f; // +10% Velocidade
    VesperConfig.PassiveBonuses.CooldownReduction = 0.08f; // +8% CDR
    VesperConfig.PassiveBonuses.ManaRegeneration = 5.0f; // +5 mana/s

    VesperConfig.ExclusiveAbility.AbilityName = FText::FromString(TEXT("Sopro de Fluxo"));
    VesperConfig.ExclusiveAbility.AbilityDescription = FText::FromString(TEXT("Dash aliado + shield"));
    VesperConfig.ExclusiveAbility.Cooldown = 30.0f;
    VesperConfig.ExclusiveAbility.ManaCost = 60.0f;
    VesperConfig.ExclusiveAbility.Duration = 2.0f;
    VesperConfig.ExclusiveAbility.Range = 1200.0f;

    // Configurar tempos de fusão e cooldowns usando UE5.6 APIs
    AegisConfig.Properties.FusionTime = 2.5f;
    AegisConfig.Properties.ReforgeCooldown = 30.0f;
    AegisConfig.Properties.SigiloLevel = 1;
    AegisConfig.Properties.SigiloExperience = 0;
    
    RuinConfig.Properties.FusionTime = 2.0f;
    RuinConfig.Properties.ReforgeCooldown = 25.0f;
    RuinConfig.Properties.SigiloLevel = 1;
    RuinConfig.Properties.SigiloExperience = 0;
    
    VesperConfig.Properties.FusionTime = 3.0f;
    VesperConfig.Properties.ReforgeCooldown = 35.0f;
    VesperConfig.Properties.SigiloLevel = 1;
    VesperConfig.Properties.SigiloExperience = 0;

    // Adicionar configurações ao array usando UE5.6 TArray API
    SigiloConfigurations.Empty(); // Limpar configurações existentes

    // Criar entradas para cada configuração
    FAuracronSigiloConfigurationEntry AegisEntry;
    AegisEntry.SigiloType = EAuracronSigiloType::Aegis;
    AegisEntry.Configuration = AegisConfig;
    SigiloConfigurations.Add(AegisEntry);

    FAuracronSigiloConfigurationEntry RuinEntry;
    RuinEntry.SigiloType = EAuracronSigiloType::Ruin;
    RuinEntry.Configuration = RuinConfig;
    SigiloConfigurations.Add(RuinEntry);

    FAuracronSigiloConfigurationEntry VesperEntry;
    VesperEntry.SigiloType = EAuracronSigiloType::Vesper;
    VesperEntry.Configuration = VesperConfig;
    SigiloConfigurations.Add(VesperEntry);

    // Validar se todas as configurações foram carregadas corretamente
    bool bAllConfigsLoaded = true;
    for (int32 i = 1; i <= 3; ++i) // Pular None (0)
    {
        EAuracronSigiloType SigiloType = static_cast<EAuracronSigiloType>(i);
        if (!SigiloConfigurations.Contains(SigiloType))
        {
            UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao carregar configuração para Sigilo %d"), i);
            bAllConfigsLoaded = false;
        }
    }

    if (bAllConfigsLoaded)
    {
        UE_LOG(LogTemp, Log, TEXT("AURACRON: %d configurações padrão dos Sígilos carregadas com sucesso"), SigiloConfigurations.Num());
        return true;
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Erro ao carregar algumas configurações dos Sígilos"));
        return false;
    }
}

// === Internal Management Methods ===

bool UAuracronSigilosBridge::InitializeSigiloSystem()
{
    if (!GetOwner())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Owner inválido para inicialização do sistema"));
        return false;
    }

    // Obter AbilitySystemComponent do Owner usando UE5.6 API
    AbilitySystemComponent = UAbilitySystemBlueprintLibrary::GetAbilitySystemComponent(GetOwner());
    if (!AbilitySystemComponent)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: AbilitySystemComponent não encontrado no Owner"));
        return false;
    }

    // Carregar configurações padrão
    if (!LoadDefaultSigiloConfigurations())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao carregar configurações padrão"));
        return false;
    }

    // Inicializar arrays de efeitos ativos
    ActiveSigiloEffects.Empty();
    ActivePassiveEffects.Empty();
    ActiveVisualEffects.Empty();
    ActiveAudioComponents.Empty();
    
    // Configurar estado inicial
    SelectedSigiloType = EAuracronSigiloType::None;
    CurrentFusionState = EAuracronSigiloFusionState::Inactive;
    FusionStartTime = 0.0f;
    LastReforgeTime = 0.0f;
    
    // Configurar timeline de fusão
    if (FusionTimeline)
    {
        FusionTimeline->SetLooping(false);
        FusionTimeline->SetIgnoreTimeDilation(false);
    }

    bSystemInitialized = true;
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Sistema de Sígilos inicializado com sucesso"));
    return true;
}

bool UAuracronSigilosBridge::ApplySigiloGameplayEffects(const FAuracronSigiloConfiguration& Configuration)
{
    if (!bSystemInitialized || !AbilitySystemComponent)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Sistema não inicializado ou ASC inválido"));
        return false;
    }

    int32 AppliedEffects = 0;
    
    // Aplicar GameplayEffects de fusão usando UE5.6 API
    for (const TSoftClassPtr<UGameplayEffect>& EffectClass : Configuration.FusionGameplayEffects)
    {
        if (EffectClass.IsValid())
        {
            TSubclassOf<UGameplayEffect> LoadedEffect = EffectClass.LoadSynchronous();
            if (LoadedEffect)
            {
                FGameplayEffectContextHandle EffectContext = AbilitySystemComponent->MakeEffectContext();
                EffectContext.AddSourceObject(GetOwner()); // Adicionar source object
                
                FGameplayEffectSpecHandle SpecHandle = AbilitySystemComponent->MakeOutgoingSpec(LoadedEffect, 1.0f, EffectContext);

                if (SpecHandle.IsValid())
                {
                    // Configurar tags do Sigilo no spec
                    if (Configuration.FusionTags.Num() > 0)
                    {
                        SpecHandle.Data->DynamicGrantedTags.AppendTags(Configuration.FusionTags);
                    }
                    
                    FActiveGameplayEffectHandle ActiveHandle = AbilitySystemComponent->ApplyGameplayEffectSpecToSelf(*SpecHandle.Data.Get());
                    if (ActiveHandle.IsValid())
                    {
                        ActiveSigiloEffects.Add(ActiveHandle);
                        AppliedEffects++;
                        UE_LOG(LogTemp, Verbose, TEXT("AURACRON: GameplayEffect aplicado: %s"), *LoadedEffect->GetName());
                    }
                }
            }
        }
    }
    
    // Aplicar tags de fusão diretamente se não foram aplicadas via GameplayEffects
    if (Configuration.FusionTags.Num() > 0)
    {
        AbilitySystemComponent->AddLooseGameplayTags(Configuration.FusionTags);
        UE_LOG(LogTemp, Verbose, TEXT("AURACRON: %d tags de fusão aplicadas"), Configuration.FusionTags.Num());
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: %d GameplayEffects do Sigilo aplicados"), AppliedEffects);
    return AppliedEffects > 0 || Configuration.FusionTags.Num() > 0;
}

bool UAuracronSigilosBridge::RemoveSigiloGameplayEffects()
{
    if (!bSystemInitialized || !AbilitySystemComponent)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Sistema não inicializado ou ASC inválido"));
        return false;
    }

    int32 RemovedEffects = 0;
    
    // Remover todos os GameplayEffects ativos do Sigilo usando UE5.6 API
    for (const FActiveGameplayEffectHandle& Handle : ActiveSigiloEffects)
    {
        if (Handle.IsValid())
        {
            bool bRemoved = AbilitySystemComponent->RemoveActiveGameplayEffect(Handle);
            if (bRemoved)
            {
                RemovedEffects++;
                UE_LOG(LogTemp, Verbose, TEXT("AURACRON: GameplayEffect removido com sucesso"));
            }
        }
    }
    ActiveSigiloEffects.Empty();
    
    // Remover tags de fusão se existirem
    const FAuracronSigiloConfigurationEntry* ConfigEntry = SigiloConfigurations.FindByPredicate([&](const FAuracronSigiloConfigurationEntry& Entry) {
        return Entry.SigiloType == SelectedSigiloType;
    });

    if (ConfigEntry && ConfigEntry->Configuration.FusionTags.Num() > 0)
    {
        AbilitySystemComponent->RemoveLooseGameplayTags(ConfigEntry->Configuration.FusionTags);
        UE_LOG(LogTemp, Verbose, TEXT("AURACRON: %d tags de fusão removidas"), ConfigEntry->Configuration.FusionTags.Num());
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: %d GameplayEffects do Sigilo removidos"), RemovedEffects);
    return true;
}

bool UAuracronSigilosBridge::UpdateVisualEffects(const FAuracronSigiloVisualEffects& VisualEffects)
{
    if (!GetOwner())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Owner inválido para efeitos visuais"));
        return false;
    }

    // Limpar efeitos visuais anteriores usando UE5.6 API
    for (UNiagaraComponent* Component : ActiveVisualEffects)
    {
        if (IsValid(Component))
        {
            Component->Deactivate();
            Component->DestroyComponent();
        }
    }
    ActiveVisualEffects.Empty();

    int32 EffectsApplied = 0;
    
    // Criar novos efeitos visuais baseados no estado atual
    UNiagaraSystem* ParticleSystem = nullptr;

    switch (CurrentFusionState)
    {
        case EAuracronSigiloFusionState::Charging:
            if (VisualEffects.FusionParticleSystem.IsValid())
            {
                ParticleSystem = VisualEffects.FusionParticleSystem.LoadSynchronous();
            }
            break;

        case EAuracronSigiloFusionState::Active:
            if (VisualEffects.ActivationParticleSystem.IsValid())
            {
                ParticleSystem = VisualEffects.ActivationParticleSystem.LoadSynchronous();
            }
            break;

        default:
            break;
    }

    if (ParticleSystem)
    {
        UNiagaraComponent* NiagaraComp = UNiagaraFunctionLibrary::SpawnSystemAttached(
            ParticleSystem,
            GetOwner()->GetRootComponent(),
            NAME_None,
            FVector::ZeroVector,
            FRotator::ZeroRotator,
            EAttachLocation::KeepRelativeOffset,
            true
        );

        if (NiagaraComp)
        {
            NiagaraComp->SetFloatParameter(FName("EffectIntensity"), VisualEffects.EffectIntensity);
            // Encontrar configuração do sigilo selecionado
            const FAuracronSigiloConfigurationEntry* ConfigEntry = SigiloConfigurations.FindByPredicate([this](const FAuracronSigiloConfigurationEntry& Entry)
            {
                return Entry.SigiloType == SelectedSigiloType;
            });

            if (ConfigEntry)
            {
                NiagaraComp->SetColorParameter(FName("PrimaryColor"), ConfigEntry->Configuration.Properties.PrimaryColor);
            }

            ActiveVisualEffects.Add(NiagaraComp);
            EffectsApplied++;
            UE_LOG(LogTemp, Verbose, TEXT("AURACRON: Sistema de partículas aplicado"));
        }
    }
    
    // Aplicar efeito de aura se disponível
    if (VisualEffects.AbilityParticleSystem.IsValid())
    {
        UNiagaraSystem* AuraSystem = VisualEffects.AbilityParticleSystem.LoadSynchronous();
        if (AuraSystem)
        {
            UNiagaraComponent* AuraComp = UNiagaraFunctionLibrary::SpawnSystemAttached(
                AuraSystem,
                GetOwner()->GetRootComponent(),
                NAME_None,
                FVector(0, 0, -50), // Offset para baixo
                FRotator::ZeroRotator,
                EAttachLocation::KeepRelativeOffset,
                true
            );

            if (AuraComp)
            {
                // Configurar parâmetros da aura
                AuraComp->SetFloatParameter(TEXT("AuraRadius"), 200.0f);
                // Encontrar configuração do sigilo selecionado
                const FAuracronSigiloConfigurationEntry* ConfigEntry = SigiloConfigurations.FindByPredicate([this](const FAuracronSigiloConfigurationEntry& Entry)
                {
                    return Entry.SigiloType == SelectedSigiloType;
                });

                if (ConfigEntry)
                {
                    AuraComp->SetColorParameter(FName("AuraColor"), ConfigEntry->Configuration.Properties.PrimaryColor);
                }
                AuraComp->SetFloatParameter(TEXT("AuraIntensity"), 0.8f);
                
                ActiveVisualEffects.Add(AuraComp);
                EffectsApplied++;
                UE_LOG(LogTemp, Verbose, TEXT("AURACRON: Sistema de partículas de aura aplicado"));
            }
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: %d efeitos visuais aplicados (%d componentes ativos)"), EffectsApplied, ActiveVisualEffects.Num());
    return EffectsApplied > 0 || ActiveVisualEffects.Num() > 0;
}

bool UAuracronSigilosBridge::PlayAudioEffects(const FAuracronSigiloAudioEffects& AudioEffects)
{
    if (!GetOwner())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Owner inválido para efeitos de áudio"));
        return false;
    }

    // Limpar componentes de áudio anteriores se necessário
    for (UAudioComponent* Component : ActiveAudioComponents)
    {
        if (IsValid(Component) && Component->IsPlaying())
        {
            Component->FadeOut(0.5f, 0.0f);
        }
    }

    UMetaSoundSource* SoundToPlay = nullptr;
    int32 AudioEffectsPlayed = 0;

    switch (CurrentFusionState)
    {
        case EAuracronSigiloFusionState::Charging:
            if (AudioEffects.FusionSound.IsValid())
            {
                SoundToPlay = Cast<UMetaSoundSource>(AudioEffects.FusionSound.LoadSynchronous());
                UE_LOG(LogTemp, Verbose, TEXT("AURACRON: Reproduzindo som de fusão"));
            }
            break;

        case EAuracronSigiloFusionState::Active:
            if (AudioEffects.ActivationSound.IsValid())
            {
                SoundToPlay = Cast<UMetaSoundSource>(AudioEffects.ActivationSound.LoadSynchronous());
                UE_LOG(LogTemp, Verbose, TEXT("AURACRON: Reproduzindo som de ativação"));
            }
            break;

        case EAuracronSigiloFusionState::Reforging:
            if (AudioEffects.ReforgeSound.IsValid())
            {
                SoundToPlay = Cast<UMetaSoundSource>(AudioEffects.ReforgeSound.LoadSynchronous());
                UE_LOG(LogTemp, Verbose, TEXT("AURACRON: Reproduzindo som de re-forjamento"));
            }
            break;

        default:
            UE_LOG(LogTemp, Verbose, TEXT("AURACRON: Nenhum som para reproduzir no estado atual"));
            break;
    }

    if (SoundToPlay)
    {
        UAudioComponent* AudioComp = UGameplayStatics::SpawnSoundAttached(
            SoundToPlay,
            GetOwner()->GetRootComponent(),
            NAME_None,
            FVector::ZeroVector,
            EAttachLocation::KeepRelativeOffset,
            false,
            AudioEffects.EffectVolume,
            1.0f // Pitch padrão
        );

        if (AudioComp)
        {
            // Configurar propriedades adicionais do áudio
            AudioComp->SetVolumeMultiplier(AudioEffects.EffectVolume);
            AudioComp->SetPitchMultiplier(1.0f);
            
            ActiveAudioComponents.Add(AudioComp);
            AudioEffectsPlayed++;
            UE_LOG(LogTemp, Verbose, TEXT("AURACRON: Componente de áudio criado e reproduzindo"));
        }
    }
    
    // Reproduzir som de ativação como ambiente se disponível
    if (AudioEffects.ActivationSound.IsValid() && CurrentFusionState == EAuracronSigiloFusionState::Active)
    {
        UMetaSoundSource* AmbientSound = Cast<UMetaSoundSource>(AudioEffects.ActivationSound.LoadSynchronous());
        if (AmbientSound)
        {
            UAudioComponent* AmbientComp = UGameplayStatics::SpawnSoundAttached(
                AmbientSound,
                GetOwner()->GetRootComponent(),
                NAME_None,
                FVector::ZeroVector,
                EAttachLocation::KeepRelativeOffset,
                false,
                AudioEffects.EffectVolume * 0.6f, // Volume reduzido para ambiente
                1.0f
            );
            
            if (AmbientComp)
            {
                AmbientComp->SetUISound(false);
                ActiveAudioComponents.Add(AmbientComp);
                AudioEffectsPlayed++;
                UE_LOG(LogTemp, Verbose, TEXT("AURACRON: Som ambiente aplicado"));
            }
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: %d efeitos de áudio reproduzidos"), AudioEffectsPlayed);
    return AudioEffectsPlayed > 0;
}

bool UAuracronSigilosBridge::ValidateSigiloSelection(EAuracronSigiloType SigiloType) const
{
    // Validar se o tipo de Sigilo é válido
    if (SigiloType == EAuracronSigiloType::None)
    {
        UE_LOG(LogTemp, Verbose, TEXT("AURACRON: Tipo de Sigilo None não é válido para seleção"));
        return false;
    }

    // Verificar se existe configuração para este Sigilo
    if (!SigiloConfigurations.Contains(SigiloType))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Configuração não encontrada para Sigilo %d"), (int32)SigiloType);
        return false;
    }
    
    // Verificar se o sistema está inicializado
    if (!bSystemInitialized)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Sistema não inicializado para validação"));
        return false;
    }
    
    // Verificar se não está em cooldown de re-forjamento (se já há um Sigilo selecionado)
    if (SelectedSigiloType != EAuracronSigiloType::None && SelectedSigiloType != SigiloType)
    {
        if (!CanReforge())
        {
            UE_LOG(LogTemp, Verbose, TEXT("AURACRON: Não é possível selecionar novo Sigilo - em cooldown de re-forjamento"));
            return false;
        }
    }
    
    // Verificar se não está em processo de fusão
    if (CurrentFusionState == EAuracronSigiloFusionState::Charging)
    {
        UE_LOG(LogTemp, Verbose, TEXT("AURACRON: Não é possível selecionar Sigilo durante fusão"));
        return false;
    }
    
    // Validação adicional: verificar se o jogador tem nível suficiente (se implementado)
    const FAuracronSigiloConfigurationEntry* ConfigEntry = SigiloConfigurations.FindByPredicate([&](const FAuracronSigiloConfigurationEntry& Entry) {
        return Entry.SigiloType == SigiloType;
    });

    if (ConfigEntry)
    {
        // Aqui poderia haver validação de nível, recursos, etc.
        // Por enquanto, sempre permitir
        UE_LOG(LogTemp, Verbose, TEXT("AURACRON: Sigilo %d validado com sucesso"), (int32)SigiloType);
        return true;
    }
    
    return false;
}

void UAuracronSigilosBridge::ProcessFusion(float DeltaTime)
{
    if (CurrentFusionState != EAuracronSigiloFusionState::Charging)
    {
        return;
    }

    // Verificar se a fusão deve ser completada automaticamente
    // (por exemplo, após um tempo específico ou condição especial)
    float ElapsedTime = GetWorld()->GetTimeSeconds() - FusionStartTime;

    // Para este exemplo, a fusão é instantânea quando iniciada
    // Em um jogo real, poderia haver uma barra de progresso ou animação
    if (ElapsedTime >= 1.0f) // 1 segundo de "carregamento"
    {
        CompleteSigiloFusion();
    }
}

void UAuracronSigilosBridge::ProcessReforgeCooldown(float DeltaTime)
{
    // Este método pode ser usado para atualizar UI ou outros sistemas
    // sobre o progresso do cooldown de re-forjamento
    float RemainingCooldown = GetReforgeCooldownRemaining();

    if (RemainingCooldown <= 0.0f && LastReforgeTime > 0.0f)
    {
        // Cooldown completado
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Cooldown de re-forjamento completado"));
    }
}

// === Replication Callbacks ===

void UAuracronSigilosBridge::OnRep_SelectedSigiloType()
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Sigilo replicado: %d"), (int32)SelectedSigiloType);

    // Atualizar efeitos visuais baseados no novo Sigilo
    const FAuracronSigiloConfigurationEntry* ConfigEntry = SigiloConfigurations.FindByPredicate([&](const FAuracronSigiloConfigurationEntry& Entry) {
        return Entry.SigiloType == SelectedSigiloType;
    });

    if (ConfigEntry)
    {
        UpdateVisualEffects(ConfigEntry->Configuration.VisualEffects);
    }

    // Broadcast evento local
    OnSigiloSelected.Broadcast(SelectedSigiloType);
}

void UAuracronSigilosBridge::OnRep_FusionState()
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Estado de fusão replicado: %d"), (int32)CurrentFusionState);

    // Atualizar efeitos baseados no novo estado
    const FAuracronSigiloConfigurationEntry* ConfigEntry = SigiloConfigurations.FindByPredicate([&](const FAuracronSigiloConfigurationEntry& Entry) {
        return Entry.SigiloType == SelectedSigiloType;
    });

    if (ConfigEntry)
    {
        UpdateVisualEffects(ConfigEntry->Configuration.VisualEffects);
        PlayAudioEffects(ConfigEntry->Configuration.AudioEffects);
    }

    // Broadcast eventos apropriados
    switch (CurrentFusionState)
    {
        case EAuracronSigiloFusionState::Charging:
            OnFusionStarted.Broadcast();
            break;

        case EAuracronSigiloFusionState::Active:
            OnFusionCompleted.Broadcast(SelectedSigiloType);
            break;

        default:
            break;
    }
}

// === Fusion 2.0 System Implementation ===

bool UAuracronSigilosBridge::EquipAegisSigil(EAuracronAegisSigilType AegisType)
{
    if (!bSystemInitialized || AegisType == EAuracronAegisSigilType::None)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Cannot equip Aegis Sigil - system not initialized or invalid type"));
        return false;
    }

    // Thread safety
    FScopeLock Lock(&SigiloMutex);

    // Update equipped Aegis Sigil
    EquippedAegisSigil.AegisSubtype = AegisType;
    EquippedAegisSigil.Level = 1;
    EquippedAegisSigil.Experience = 0;
    EquippedAegisSigil.CurrentCooldown = 0.0f;
    EquippedAegisSigil.bIsActive = false;

    // Update current archetype
    UpdateCurrentArchetype();

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Aegis Sigil equipped: %d"), (int32)AegisType);

    // Broadcast event
    OnSigilEquipped.Broadcast(EAuracronSigiloType::Aegis, static_cast<int32>(AegisType), EquippedAegisSigil.Level);

    return true;
}

bool UAuracronSigilosBridge::EquipRuinSigil(EAuracronRuinSigilType RuinType)
{
    if (!bSystemInitialized || RuinType == EAuracronRuinSigilType::None)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Cannot equip Ruin Sigil - system not initialized or invalid type"));
        return false;
    }

    // Thread safety
    FScopeLock Lock(&SigiloMutex);

    // Update equipped Ruin Sigil
    EquippedRuinSigil.RuinSubtype = RuinType;
    EquippedRuinSigil.Level = 1;
    EquippedRuinSigil.Experience = 0;
    EquippedRuinSigil.CurrentCooldown = 0.0f;
    EquippedRuinSigil.bIsActive = false;

    // Update current archetype
    UpdateCurrentArchetype();

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Ruin Sigil equipped: %d"), (int32)RuinType);

    // Broadcast event
    OnSigilEquipped.Broadcast(EAuracronSigiloType::Ruin, static_cast<int32>(RuinType), EquippedRuinSigil.Level);

    return true;
}

bool UAuracronSigilosBridge::EquipVesperSigil(EAuracronVesperSigilType VesperType)
{
    if (!bSystemInitialized || VesperType == EAuracronVesperSigilType::None)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Cannot equip Vesper Sigil - system not initialized or invalid type"));
        return false;
    }

    // Thread safety
    FScopeLock Lock(&SigiloMutex);

    // Update equipped Vesper Sigil
    EquippedVesperSigil.VesperSubtype = VesperType;
    EquippedVesperSigil.Level = 1;
    EquippedVesperSigil.Experience = 0;
    EquippedVesperSigil.CurrentCooldown = 0.0f;
    EquippedVesperSigil.bIsActive = false;

    // Update current archetype
    UpdateCurrentArchetype();

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Vesper Sigil equipped: %d"), (int32)VesperType);

    // Broadcast event
    OnSigilEquipped.Broadcast(EAuracronSigiloType::Vesper, static_cast<int32>(VesperType), EquippedVesperSigil.Level);

    return true;
}

// === Sigil Activation Methods ===

bool UAuracronSigilosBridge::ActivateAegisSigil()
{
    if (!bSystemInitialized || !AbilitySystemComponent)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Cannot activate Aegis Sigil - system not initialized"));
        return false;
    }

    if (EquippedAegisSigil.AegisSubtype == EAuracronAegisSigilType::None)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: No Aegis Sigil equipped"));
        return false;
    }

    if (EquippedAegisSigil.CurrentCooldown > 0.0f)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Aegis Sigil is on cooldown: %.1f seconds"), EquippedAegisSigil.CurrentCooldown);
        return false;
    }

    // Apply Aegis Sigil effects based on subtype
    bool bSuccess = ApplyAegisSigilEffects(EquippedAegisSigil.AegisSubtype);

    if (bSuccess)
    {
        EquippedAegisSigil.bIsActive = true;
        EquippedAegisSigil.CurrentCooldown = GetAegisSigilCooldown(EquippedAegisSigil.AegisSubtype);

        // Add GameplayTag
        FGameplayTag AegisTag = AuracronSigilTags::GetAegisSigilTag(EquippedAegisSigil.AegisSubtype);
        AbilitySystemComponent->AddLooseGameplayTag(AegisTag);

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Aegis Sigil activated: %d"), (int32)EquippedAegisSigil.AegisSubtype);

        // Broadcast event
        float Duration = GetAegisSigilDuration(EquippedAegisSigil.AegisSubtype);
        OnSigilActivated.Broadcast(EAuracronSigiloType::Aegis, Duration);

        // Set timer to deactivate
        GetWorld()->GetTimerManager().SetTimer(
            AegisShieldTimerHandle,
            [this]()
            {
                DeactivateAegisSigil();
            },
            Duration,
            false
        );
    }

    return bSuccess;
}

bool UAuracronSigilosBridge::ActivateRuinSigil()
{
    if (!bSystemInitialized || !AbilitySystemComponent)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Cannot activate Ruin Sigil - system not initialized"));
        return false;
    }

    if (EquippedRuinSigil.RuinSubtype == EAuracronRuinSigilType::None)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: No Ruin Sigil equipped"));
        return false;
    }

    if (EquippedRuinSigil.CurrentCooldown > 0.0f)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Ruin Sigil is on cooldown: %.1f seconds"), EquippedRuinSigil.CurrentCooldown);
        return false;
    }

    // Apply Ruin Sigil effects based on subtype
    bool bSuccess = ApplyRuinSigilEffects(EquippedRuinSigil.RuinSubtype);

    if (bSuccess)
    {
        EquippedRuinSigil.bIsActive = true;
        EquippedRuinSigil.CurrentCooldown = GetRuinSigilCooldown(EquippedRuinSigil.RuinSubtype);

        // Add GameplayTag
        FGameplayTag RuinTag = AuracronSigilTags::GetRuinSigilTag(EquippedRuinSigil.RuinSubtype);
        AbilitySystemComponent->AddLooseGameplayTag(RuinTag);

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Ruin Sigil activated: %d"), (int32)EquippedRuinSigil.RuinSubtype);

        // Broadcast event
        float Duration = GetRuinSigilDuration(EquippedRuinSigil.RuinSubtype);
        OnSigilActivated.Broadcast(EAuracronSigiloType::Ruin, Duration);

        // Set timer to deactivate
        GetWorld()->GetTimerManager().SetTimer(
            RuinDamageTimerHandle,
            [this]()
            {
                DeactivateRuinSigil();
            },
            Duration,
            false
        );
    }

    return bSuccess;
}

bool UAuracronSigilosBridge::ActivateVesperSigil()
{
    if (!bSystemInitialized || !AbilitySystemComponent)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Cannot activate Vesper Sigil - system not initialized"));
        return false;
    }

    if (EquippedVesperSigil.VesperSubtype == EAuracronVesperSigilType::None)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: No Vesper Sigil equipped"));
        return false;
    }

    if (EquippedVesperSigil.CurrentCooldown > 0.0f)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Vesper Sigil is on cooldown: %.1f seconds"), EquippedVesperSigil.CurrentCooldown);
        return false;
    }

    // Apply Vesper Sigil effects based on subtype
    bool bSuccess = ApplyVesperSigilEffects(EquippedVesperSigil.VesperSubtype);

    if (bSuccess)
    {
        EquippedVesperSigil.bIsActive = true;
        EquippedVesperSigil.CurrentCooldown = GetVesperSigilCooldown(EquippedVesperSigil.VesperSubtype);

        // Add GameplayTag
        FGameplayTag VesperTag = AuracronSigilTags::GetVesperSigilTag(EquippedVesperSigil.VesperSubtype);
        AbilitySystemComponent->AddLooseGameplayTag(VesperTag);

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Vesper Sigil activated: %d"), (int32)EquippedVesperSigil.VesperSubtype);

        // Broadcast event
        float Duration = GetVesperSigilDuration(EquippedVesperSigil.VesperSubtype);
        OnSigilActivated.Broadcast(EAuracronSigiloType::Vesper, Duration);

        // Set timer to deactivate using UE 5.6 API
        FTimerHandle VesperDeactivationTimer;
        GetWorld()->GetTimerManager().SetTimer(
            VesperDeactivationTimer,
            [this]()
            {
                DeactivateVesperSigil();
            },
            Duration,
            false
        );
    }

    return bSuccess;
}

bool UAuracronSigilosBridge::ActivateFusion20()
{
    if (!bSystemInitialized || !AbilitySystemComponent)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Cannot activate Fusion 2.0 - system not initialized"));
        return false;
    }

    if (!CanActivateFusion20())
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Cannot activate Fusion 2.0 - requirements not met"));
        return false;
    }

    // Thread safety
    FScopeLock Lock(&SigiloMutex);

    // Activate all three Sigils simultaneously
    bool bAegisActivated = ActivateAegisSigil();
    bool bRuinActivated = ActivateRuinSigil();
    bool bVesperActivated = ActivateVesperSigil();

    if (bAegisActivated && bRuinActivated && bVesperActivated)
    {
        bFusion20Active = true;
        Fusion20StartTime = GetWorld()->GetTimeSeconds();
        LastFusion20Time = Fusion20StartTime;

        // Apply Archetype-specific effects
        ApplyArchetypeEffects(CurrentArchetype);

        // Add Fusion 2.0 tags
        AbilitySystemComponent->AddLooseGameplayTag(AuracronSigilTags::Sigil_State_Fusion20);
        AbilitySystemComponent->AddLooseGameplayTag(AuracronSigilTags::Sigil_Fusion_Active);

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Fusion 2.0 activated! Archetype: %s"), *CurrentArchetype.ArchetypeName.ToString());

        // Broadcast event
        OnFusion20Activated.Broadcast(CurrentArchetype, Fusion20Duration);

        // Set timer to end Fusion 2.0
        GetWorld()->GetTimerManager().SetTimer(
            Fusion20DurationTimer,
            [this]()
            {
                EndFusion20();
            },
            Fusion20Duration,
            false
        );

        return true;
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Failed to activate all Sigils for Fusion 2.0"));
        return false;
    }
}

void UAuracronSigilosBridge::ActivateFusion20Input(const FInputActionValue& Value)
{
    // Enhanced Input wrapper for ActivateFusion20
    ActivateFusion20();
}

bool UAuracronSigilosBridge::CanActivateFusion20() const
{
    // Check if all three Sigils are equipped
    if (EquippedAegisSigil.AegisSubtype == EAuracronAegisSigilType::None ||
        EquippedRuinSigil.RuinSubtype == EAuracronRuinSigilType::None ||
        EquippedVesperSigil.VesperSubtype == EAuracronVesperSigilType::None)
    {
        return false;
    }

    // Check if Fusion 2.0 is not on cooldown
    if (LastFusion20Time > 0.0f)
    {
        float CurrentTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;
        float TimeSinceLastFusion = CurrentTime - LastFusion20Time;
        if (TimeSinceLastFusion < Fusion20Cooldown)
        {
            return false;
        }
    }

    // Check if no individual Sigils are on cooldown
    if (EquippedAegisSigil.CurrentCooldown > 0.0f ||
        EquippedRuinSigil.CurrentCooldown > 0.0f ||
        EquippedVesperSigil.CurrentCooldown > 0.0f)
    {
        return false;
    }

    // Check if Fusion 2.0 is not already active
    if (bFusion20Active)
    {
        return false;
    }

    return true;
}

FAuracronSigilArchetype UAuracronSigilosBridge::GetCurrentArchetype() const
{
    return CurrentArchetype;
}

TArray<FAuracronSigilArchetype> UAuracronSigilosBridge::GetAvailableArchetypes() const
{
    return AvailableArchetypes;
}

FText UAuracronSigilosBridge::GenerateArchetypeName(EAuracronAegisSigilType AegisType, EAuracronRuinSigilType RuinType, EAuracronVesperSigilType VesperType) const
{
    // Generate unique archetype names based on combinations
    FString AegisName = GetAegisSigilName(AegisType);
    FString RuinName = GetRuinSigilName(RuinType);
    FString VesperName = GetVesperSigilName(VesperType);

    // Create compound name based on dominant characteristics
    FString ArchetypeName;

    // Determine archetype category based on combination
    if (AegisType == EAuracronAegisSigilType::Absoluto && RuinType == EAuracronRuinSigilType::Aniquilador)
    {
        ArchetypeName = TEXT("Destruidor Absoluto");
    }
    else if (AegisType == EAuracronAegisSigilType::Temporal && VesperType == EAuracronVesperSigilType::Temporal)
    {
        ArchetypeName = TEXT("Mestre Temporal");
    }
    else if (RuinType == EAuracronRuinSigilType::Sombrio && VesperType == EAuracronVesperSigilType::Teleporte)
    {
        ArchetypeName = TEXT("Assassino Sombrio");
    }
    else if (AegisType == EAuracronAegisSigilType::Cristalino && VesperType == EAuracronVesperSigilType::Curativo)
    {
        ArchetypeName = TEXT("Guardião Cristalino");
    }
    else
    {
        // Generate compound name from components
        ArchetypeName = FString::Printf(TEXT("%s %s"), *AegisName, *RuinName);
    }

    return FText::FromString(ArchetypeName);
}

// === Auxiliary Methods for Fusion 2.0 ===

void UAuracronSigilosBridge::UpdateCurrentArchetype()
{
    if (EquippedAegisSigil.AegisSubtype == EAuracronAegisSigilType::None ||
        EquippedRuinSigil.RuinSubtype == EAuracronRuinSigilType::None ||
        EquippedVesperSigil.VesperSubtype == EAuracronVesperSigilType::None)
    {
        // Clear archetype if not all Sigils are equipped
        CurrentArchetype = FAuracronSigilArchetype();
        return;
    }

    // Generate archetype based on current combination
    CurrentArchetype.AegisComponent = EquippedAegisSigil.AegisSubtype;
    CurrentArchetype.RuinComponent = EquippedRuinSigil.RuinSubtype;
    CurrentArchetype.VesperComponent = EquippedVesperSigil.VesperSubtype;

    // Generate unique tag
    CurrentArchetype.ArchetypeTag = AuracronSigilTags::GenerateArchetypeTag(
        EquippedAegisSigil.AegisSubtype,
        EquippedRuinSigil.RuinSubtype,
        EquippedVesperSigil.VesperSubtype
    );

    // Generate name and description
    CurrentArchetype.ArchetypeName = GenerateArchetypeName(
        EquippedAegisSigil.AegisSubtype,
        EquippedRuinSigil.RuinSubtype,
        EquippedVesperSigil.VesperSubtype
    );

    CurrentArchetype.ArchetypeDescription = GenerateArchetypeDescription(
        EquippedAegisSigil.AegisSubtype,
        EquippedRuinSigil.RuinSubtype,
        EquippedVesperSigil.VesperSubtype
    );

    // Calculate archetype properties
    CurrentArchetype.PowerMultiplier = CalculateArchetypePowerMultiplier();
    CurrentArchetype.CooldownReduction = CalculateArchetypeCooldownReduction();
    CurrentArchetype.EnergyEfficiency = CalculateArchetypeEnergyEfficiency();

    // Set required level (based on highest Sigil level)
    CurrentArchetype.RequiredLevel = FMath::Max3(
        EquippedAegisSigil.Level,
        EquippedRuinSigil.Level,
        EquippedVesperSigil.Level
    );

    // Generate colors based on combination
    CurrentArchetype.PrimaryColor = BlendSigilColors();
    CurrentArchetype.SecondaryColor = CurrentArchetype.PrimaryColor * 0.7f;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Archetype updated: %s"), *CurrentArchetype.ArchetypeName.ToString());

    // Broadcast archetype formation
    OnArchetypeFormed.Broadcast(CurrentArchetype);
}

bool UAuracronSigilosBridge::ApplyAegisSigilEffects(EAuracronAegisSigilType AegisType)
{
    if (!AbilitySystemComponent)
    {
        return false;
    }

    // Apply effects based on Aegis Sigil type using UE 5.6 GameplayEffect system
    switch (AegisType)
    {
        case EAuracronAegisSigilType::Primordial:
            return ApplyPrimordialAegisEffect();
        case EAuracronAegisSigilType::Cristalino:
            return ApplyCristalinoAegisEffect();
        case EAuracronAegisSigilType::Temporal:
            return ApplyTemporalAegisEffect();
        case EAuracronAegisSigilType::Espectral:
            return ApplyEspectralAegisEffect();
        case EAuracronAegisSigilType::Absoluto:
            return ApplyAbsolutoAegisEffect();
        default:
            return false;
    }
}

bool UAuracronSigilosBridge::ApplyRuinSigilEffects(EAuracronRuinSigilType RuinType)
{
    if (!AbilitySystemComponent)
    {
        return false;
    }

    // Apply effects based on Ruin Sigil type using UE 5.6 GameplayEffect system
    switch (RuinType)
    {
        case EAuracronRuinSigilType::Flamejante:
            return ApplyFlamejanteRuinEffect();
        case EAuracronRuinSigilType::Gelido:
            return ApplyGelidoRuinEffect();
        case EAuracronRuinSigilType::Sombrio:
            return ApplySombrioRuinEffect();
        case EAuracronRuinSigilType::Corrosivo:
            return ApplyCorrosivoRuinEffect();
        case EAuracronRuinSigilType::Aniquilador:
            return ApplyAniquiladorRuinEffect();
        default:
            return false;
    }
}

bool UAuracronSigilosBridge::ApplyVesperSigilEffects(EAuracronVesperSigilType VesperType)
{
    if (!AbilitySystemComponent)
    {
        return false;
    }

    // Apply effects based on Vesper Sigil type using UE 5.6 GameplayEffect system
    switch (VesperType)
    {
        case EAuracronVesperSigilType::Curativo:
            return ApplyCurativoVesperEffect();
        case EAuracronVesperSigilType::Energetico:
            return ApplyEnergeticoVesperEffect();
        case EAuracronVesperSigilType::Velocidade:
            return ApplyVelocidadeVesperEffect();
        case EAuracronVesperSigilType::Visao:
            return ApplyVisaoVesperEffect();
        case EAuracronVesperSigilType::Teleporte:
            return ApplyTeleporteVesperEffect();
        case EAuracronVesperSigilType::Temporal:
            return ApplyTemporalVesperEffect();
        default:
            return false;
    }
}

// === Specific Aegis Sigil Effect Implementations ===

bool UAuracronSigilosBridge::ApplyPrimordialAegisEffect()
{
    if (!AbilitySystemComponent)
    {
        return false;
    }

    // Create GameplayEffect for Aegis Primordial using UE 5.6 APIs
    FGameplayEffectContextHandle EffectContext = AbilitySystemComponent->MakeEffectContext();
    EffectContext.AddSourceObject(this);

    // Create shield effect spec using UE 5.6 API
    UGameplayEffect* ShieldEffect = GetAegisPrimordialGameplayEffect().GetDefaultObject();
    FGameplayEffectSpec ShieldSpec(ShieldEffect, EffectContext, EquippedAegisSigil.Level);

    // Apply shield strength based on level
    float ShieldStrength = 200.0f * (1.0f + (EquippedAegisSigil.Level - 1) * 0.1f);
    ShieldSpec.SetSetByCallerMagnitude(AuracronSigilTags::Sigil_Effect_Shield, ShieldStrength);

    FActiveGameplayEffectHandle EffectHandle = AbilitySystemComponent->ApplyGameplayEffectSpecToSelf(ShieldSpec);
    if (EffectHandle.IsValid())
    {
        ActiveAegisSigilEffects.Add(EffectHandle);

        // Spawn visual effects
        SpawnAegisSigilVFX(EAuracronAegisSigilType::Primordial);
        PlayAegisSigilAudio(EAuracronAegisSigilType::Primordial);

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Aegis Primordial effect applied - Shield: %.1f"), ShieldStrength);
        return true;
    }

    return false;
}

bool UAuracronSigilosBridge::ApplyCristalinoAegisEffect()
{
    if (!AbilitySystemComponent)
    {
        return false;
    }

    // Create GameplayEffect for Aegis Cristalino
    FGameplayEffectContextHandle EffectContext = AbilitySystemComponent->MakeEffectContext();
    EffectContext.AddSourceObject(this);

    // Create reflection shield effect spec using UE 5.6 API
    UGameplayEffect* ReflectionEffect = GetAegisCristalinoGameplayEffect().GetDefaultObject();
    FGameplayEffectSpec ReflectionSpec(ReflectionEffect, EffectContext, EquippedAegisSigil.Level);

    // Apply reflection parameters
    float ReflectionRate = 0.4f * (1.0f + (EquippedAegisSigil.Level - 1) * 0.02f);
    ReflectionSpec.SetSetByCallerMagnitude(AuracronSigilTags::Sigil_Effect_Shield, ReflectionRate);

    FActiveGameplayEffectHandle EffectHandle = AbilitySystemComponent->ApplyGameplayEffectSpecToSelf(ReflectionSpec);
    if (EffectHandle.IsValid())
    {
        ActiveAegisSigilEffects.Add(EffectHandle);

        // Spawn visual effects
        SpawnAegisSigilVFX(EAuracronAegisSigilType::Cristalino);
        PlayAegisSigilAudio(EAuracronAegisSigilType::Cristalino);

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Aegis Cristalino effect applied - Reflection: %.1f%%"), ReflectionRate * 100.0f);
        return true;
    }

    return false;
}

bool UAuracronSigilosBridge::ApplyTemporalAegisEffect()
{
    if (!AbilitySystemComponent)
    {
        return false;
    }

    // Create GameplayEffect for Aegis Temporal
    FGameplayEffectContextHandle EffectContext = AbilitySystemComponent->MakeEffectContext();
    EffectContext.AddSourceObject(this);

    // Create time dilation shield effect spec using UE 5.6 API
    UGameplayEffect* TemporalEffect = GetAegisTemporalGameplayEffect().GetDefaultObject();
    FGameplayEffectSpec TemporalSpec(TemporalEffect, EffectContext, EquippedAegisSigil.Level);

    // Apply time dilation parameters
    float TimeDilation = 0.3f * (1.0f + (EquippedAegisSigil.Level - 1) * 0.01f);
    TemporalSpec.SetSetByCallerMagnitude(AuracronSigilTags::Sigil_Effect_TimeManipulation, TimeDilation);

    FActiveGameplayEffectHandle EffectHandle = AbilitySystemComponent->ApplyGameplayEffectSpecToSelf(TemporalSpec);
    if (EffectHandle.IsValid())
    {
        ActiveAegisSigilEffects.Add(EffectHandle);

        // Apply time dilation to nearby projectiles
        ApplyProjectileTimeDilation();

        // Spawn visual effects
        SpawnAegisSigilVFX(EAuracronAegisSigilType::Temporal);
        PlayAegisSigilAudio(EAuracronAegisSigilType::Temporal);

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Aegis Temporal effect applied - Time Dilation: %.1f%%"), TimeDilation * 100.0f);
        return true;
    }

    return false;
}

bool UAuracronSigilosBridge::ApplyEspectralAegisEffect()
{
    if (!AbilitySystemComponent)
    {
        return false;
    }

    // Create GameplayEffect for Aegis Espectral
    FGameplayEffectContextHandle EffectContext = AbilitySystemComponent->MakeEffectContext();
    EffectContext.AddSourceObject(this);

    // Create magic absorption shield effect spec using UE 5.6 API
    UGameplayEffect* EspectralEffect = GetAegisEspectralGameplayEffect().GetDefaultObject();
    FGameplayEffectSpec EspectralSpec(EspectralEffect, EffectContext, EquippedAegisSigil.Level);

    // Apply magic absorption parameters
    float MagicAbsorption = 0.9f * (1.0f + (EquippedAegisSigil.Level - 1) * 0.005f);
    EspectralSpec.SetSetByCallerMagnitude(AuracronSigilTags::Sigil_Effect_Shield, MagicAbsorption);

    FActiveGameplayEffectHandle EffectHandle = AbilitySystemComponent->ApplyGameplayEffectSpecToSelf(EspectralSpec);
    if (EffectHandle.IsValid())
    {
        ActiveAegisSigilEffects.Add(EffectHandle);

        // Grant magic immunity tags
        AbilitySystemComponent->AddLooseGameplayTag(AuracronSigilTags::Sigil_Effect_Shield);

        // Spawn visual effects
        SpawnAegisSigilVFX(EAuracronAegisSigilType::Espectral);
        PlayAegisSigilAudio(EAuracronAegisSigilType::Espectral);

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Aegis Espectral effect applied - Magic Absorption: %.1f%%"), MagicAbsorption * 100.0f);
        return true;
    }

    return false;
}

bool UAuracronSigilosBridge::ApplyAbsolutoAegisEffect()
{
    if (!AbilitySystemComponent)
    {
        return false;
    }

    // Create GameplayEffect for Aegis Absoluto
    FGameplayEffectContextHandle EffectContext = AbilitySystemComponent->MakeEffectContext();
    EffectContext.AddSourceObject(this);

    // Create invulnerability effect spec using UE 5.6 API
    UGameplayEffect* AbsolutoEffect = GetAegisAbsolutoGameplayEffect().GetDefaultObject();
    FGameplayEffectSpec AbsolutoSpec(AbsolutoEffect, EffectContext, EquippedAegisSigil.Level);

    // Apply invulnerability duration
    float InvulnerabilityDuration = 3.0f + (EquippedAegisSigil.Level - 1) * 0.1f;
    AbsolutoSpec.SetSetByCallerMagnitude(AuracronSigilTags::Sigil_Effect_Shield, InvulnerabilityDuration);

    FActiveGameplayEffectHandle EffectHandle = AbilitySystemComponent->ApplyGameplayEffectSpecToSelf(AbsolutoSpec);
    if (EffectHandle.IsValid())
    {
        ActiveAegisSigilEffects.Add(EffectHandle);

        // Grant temporary invulnerability
        GrantTemporaryInvulnerability(InvulnerabilityDuration);

        // Spawn visual effects
        SpawnAegisSigilVFX(EAuracronAegisSigilType::Absoluto);
        PlayAegisSigilAudio(EAuracronAegisSigilType::Absoluto);

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Aegis Absoluto effect applied - Invulnerability: %.1f seconds"), InvulnerabilityDuration);
        return true;
    }

    return false;
}

// === Fusion 2.0 Replication Callbacks ===

void UAuracronSigilosBridge::OnRep_EquippedSigils()
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Equipped Sigils replicated"));

    // Update archetype when sigils change
    UpdateCurrentArchetype();

    // Update UI
    OnSigilEquipped.Broadcast(EAuracronSigiloType::Aegis, static_cast<int32>(EquippedAegisSigil.AegisSubtype), EquippedAegisSigil.Level);
    OnSigilEquipped.Broadcast(EAuracronSigiloType::Ruin, static_cast<int32>(EquippedRuinSigil.RuinSubtype), EquippedRuinSigil.Level);
    OnSigilEquipped.Broadcast(EAuracronSigiloType::Vesper, static_cast<int32>(EquippedVesperSigil.VesperSubtype), EquippedVesperSigil.Level);
}

void UAuracronSigilosBridge::OnRep_CurrentArchetype()
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Current Archetype replicated: %s"), *CurrentArchetype.ArchetypeName.ToString());

    // Broadcast archetype formation
    OnArchetypeFormed.Broadcast(CurrentArchetype);

    // Update visual effects if archetype is active
    if (bFusion20Active)
    {
        ApplyArchetypeVisualEffects(CurrentArchetype);
    }
}

void UAuracronSigilosBridge::OnRep_Fusion20Active()
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Fusion 2.0 Active state replicated: %s"), bFusion20Active ? TEXT("Active") : TEXT("Inactive"));

    if (bFusion20Active)
    {
        // Apply fusion visual effects
        ApplyFusion20VisualEffects();

        // Broadcast activation
        OnFusion20Activated.Broadcast(CurrentArchetype, Fusion20Duration);

        // Set timer for client-side duration tracking
        GetWorld()->GetTimerManager().SetTimer(
            Fusion20DurationTimer,
            [this]()
            {
                if (!bFusion20Active) // Server already ended it
                {
                    OnFusion20Ended.Broadcast(CurrentArchetype);
                }
            },
            Fusion20Duration,
            false
        );
    }
    else
    {
        // Remove fusion visual effects
        RemoveFusion20VisualEffects();

        // Broadcast deactivation
        OnFusion20Ended.Broadcast(CurrentArchetype);

        // Clear timer
        GetWorld()->GetTimerManager().ClearTimer(Fusion20DurationTimer);
    }
}

// === Deactivation Methods ===

void UAuracronSigilosBridge::DeactivateAegisSigil()
{
    if (!AbilitySystemComponent)
    {
        return;
    }

    // Remove active effects
    for (const FActiveGameplayEffectHandle& EffectHandle : ActiveAegisSigilEffects)
    {
        AbilitySystemComponent->RemoveActiveGameplayEffect(EffectHandle);
    }
    ActiveAegisSigilEffects.Empty();

    // Remove GameplayTags
    FGameplayTag AegisTag = AuracronSigilTags::GetAegisSigilTag(EquippedAegisSigil.AegisSubtype);
    AbilitySystemComponent->RemoveLooseGameplayTag(AegisTag);

    // Update state
    EquippedAegisSigil.bIsActive = false;

    // Remove visual effects
    RemoveAegisSigilVFX();

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Aegis Sigil deactivated"));
}

void UAuracronSigilosBridge::DeactivateRuinSigil()
{
    if (!AbilitySystemComponent)
    {
        return;
    }

    // Remove active effects
    for (const FActiveGameplayEffectHandle& EffectHandle : ActiveRuinSigilEffects)
    {
        AbilitySystemComponent->RemoveActiveGameplayEffect(EffectHandle);
    }
    ActiveRuinSigilEffects.Empty();

    // Remove GameplayTags
    FGameplayTag RuinTag = AuracronSigilTags::GetRuinSigilTag(EquippedRuinSigil.RuinSubtype);
    AbilitySystemComponent->RemoveLooseGameplayTag(RuinTag);

    // Update state
    EquippedRuinSigil.bIsActive = false;

    // Remove visual effects
    RemoveRuinSigilVFX();

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Ruin Sigil deactivated"));
}

void UAuracronSigilosBridge::DeactivateVesperSigil()
{
    if (!AbilitySystemComponent)
    {
        return;
    }

    // Remove active effects
    for (const FActiveGameplayEffectHandle& EffectHandle : ActiveVesperSigilEffects)
    {
        AbilitySystemComponent->RemoveActiveGameplayEffect(EffectHandle);
    }
    ActiveVesperSigilEffects.Empty();

    // Remove GameplayTags
    FGameplayTag VesperTag = AuracronSigilTags::GetVesperSigilTag(EquippedVesperSigil.VesperSubtype);
    AbilitySystemComponent->RemoveLooseGameplayTag(VesperTag);

    // Update state
    EquippedVesperSigil.bIsActive = false;

    // Remove visual effects
    RemoveVesperSigilVFX();

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Vesper Sigil deactivated"));
}

void UAuracronSigilosBridge::EndFusion20()
{
    if (!bFusion20Active)
    {
        return;
    }

    // Thread safety
    FScopeLock Lock(&SigiloMutex);

    // Deactivate all sigils
    DeactivateAegisSigil();
    DeactivateRuinSigil();
    DeactivateVesperSigil();

    // Remove archetype effects
    RemoveArchetypeEffects();

    // Update state
    bFusion20Active = false;

    // Remove Fusion 2.0 tags
    if (AbilitySystemComponent)
    {
        AbilitySystemComponent->RemoveLooseGameplayTag(AuracronSigilTags::Sigil_State_Fusion20);
        AbilitySystemComponent->RemoveLooseGameplayTag(AuracronSigilTags::Sigil_Fusion_Active);
        AbilitySystemComponent->AddLooseGameplayTag(AuracronSigilTags::Sigil_Fusion_Cooldown);
    }

    // Start cooldown timer
    GetWorld()->GetTimerManager().SetTimer(
        Fusion20CooldownTimer,
        [this]()
        {
            if (AbilitySystemComponent)
            {
                AbilitySystemComponent->RemoveLooseGameplayTag(AuracronSigilTags::Sigil_Fusion_Cooldown);
            }
        },
        Fusion20Cooldown,
        false
    );

    // Remove visual effects
    RemoveFusion20VisualEffects();

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Fusion 2.0 ended - Cooldown: %.1f seconds"), Fusion20Cooldown);

    // Broadcast event
    OnFusion20Ended.Broadcast(CurrentArchetype);
}

// === Specific Ruin Sigil Effect Implementations ===

bool UAuracronSigilosBridge::ApplyFlamejanteRuinEffect()
{
    if (!AbilitySystemComponent)
    {
        return false;
    }

    // Create GameplayEffect for Ruin Flamejante using UE 5.6 APIs
    FGameplayEffectContextHandle EffectContext = AbilitySystemComponent->MakeEffectContext();
    EffectContext.AddSourceObject(this);
    EffectContext.AddActors({GetOwner()}, false);

    // Create fire damage over time effect spec using UE 5.6 API
    UGameplayEffect* FireEffect = GetRuinFlamejanteGameplayEffect().GetDefaultObject();
    FGameplayEffectSpec FireDamageSpec(FireEffect, EffectContext, EquippedRuinSigil.Level);

    // Apply fire damage parameters based on level
    float BaseDamage = 80.0f * (1.0f + (EquippedRuinSigil.Level - 1) * 0.12f);
    float DamagePerSecond = 25.0f * (1.0f + (EquippedRuinSigil.Level - 1) * 0.08f);
    float SpreadRadius = 300.0f + (EquippedRuinSigil.Level - 1) * 10.0f;

    FireDamageSpec.SetSetByCallerMagnitude(AuracronSigilTags::Sigil_Effect_Damage, BaseDamage);
    FireDamageSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Sigil.Effect.DOT")), DamagePerSecond);
    FireDamageSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Sigil.Effect.Radius")), SpreadRadius);

    FActiveGameplayEffectHandle EffectHandle = AbilitySystemComponent->ApplyGameplayEffectSpecToSelf(FireDamageSpec);
    if (EffectHandle.IsValid())
    {
        ActiveRuinSigilEffects.Add(EffectHandle);

        // Apply spreading fire effect to nearby enemies
        ApplySpreadingFireEffect(SpreadRadius, DamagePerSecond);

        // Spawn visual effects
        SpawnRuinSigilVFX(EAuracronRuinSigilType::Flamejante);
        PlayRuinSigilAudio(EAuracronRuinSigilType::Flamejante);

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Ruin Flamejante effect applied - Damage: %.1f, DoT: %.1f/s, Radius: %.1f"),
            BaseDamage, DamagePerSecond, SpreadRadius);
        return true;
    }

    return false;
}

bool UAuracronSigilosBridge::ApplyGelidoRuinEffect()
{
    if (!AbilitySystemComponent)
    {
        return false;
    }

    // Create GameplayEffect for Ruin Gélido
    FGameplayEffectContextHandle EffectContext = AbilitySystemComponent->MakeEffectContext();
    EffectContext.AddSourceObject(this);
    EffectContext.AddActors({GetOwner()}, false);

    // Create ice damage and slow effect spec using UE 5.6 API
    UGameplayEffect* IceEffect = GetRuinGelidoGameplayEffect().GetDefaultObject();
    FGameplayEffectSpec IceDamageSpec(IceEffect, EffectContext, EquippedRuinSigil.Level);

    // Apply ice damage parameters
    float BaseDamage = 70.0f * (1.0f + (EquippedRuinSigil.Level - 1) * 0.1f);
    float SlowPercentage = 0.5f + (EquippedRuinSigil.Level - 1) * 0.02f;
    float FreezeChance = 0.15f + (EquippedRuinSigil.Level - 1) * 0.01f;

    IceDamageSpec.SetSetByCallerMagnitude(AuracronSigilTags::Sigil_Effect_Damage, BaseDamage);
    IceDamageSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Sigil.Effect.Slow")), SlowPercentage);
    IceDamageSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Sigil.Effect.Freeze")), FreezeChance);

    FActiveGameplayEffectHandle EffectHandle = AbilitySystemComponent->ApplyGameplayEffectSpecToSelf(IceDamageSpec);
    if (EffectHandle.IsValid())
    {
        ActiveRuinSigilEffects.Add(EffectHandle);

        // Apply ice field effect around player
        ApplyIceFieldEffect(400.0f, SlowPercentage, FreezeChance);

        // Spawn visual effects
        SpawnRuinSigilVFX(EAuracronRuinSigilType::Gelido);
        PlayRuinSigilAudio(EAuracronRuinSigilType::Gelido);

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Ruin Gélido effect applied - Damage: %.1f, Slow: %.1f%%, Freeze: %.1f%%"),
            BaseDamage, SlowPercentage * 100.0f, FreezeChance * 100.0f);
        return true;
    }

    return false;
}

bool UAuracronSigilosBridge::ApplySombrioRuinEffect()
{
    if (!AbilitySystemComponent)
    {
        return false;
    }

    // Create GameplayEffect for Ruin Sombrio
    FGameplayEffectContextHandle EffectContext = AbilitySystemComponent->MakeEffectContext();
    EffectContext.AddSourceObject(this);
    EffectContext.AddActors({GetOwner()}, false);

    // Create shadow damage and vision reduction effect spec using UE 5.6 API
    UGameplayEffect* ShadowEffect = GetRuinSombrioGameplayEffect().GetDefaultObject();
    FGameplayEffectSpec ShadowDamageSpec(ShadowEffect, EffectContext, EquippedRuinSigil.Level);

    // Apply shadow damage parameters
    float BaseDamage = 90.0f * (1.0f + (EquippedRuinSigil.Level - 1) * 0.11f);
    float VisionReduction = 0.6f + (EquippedRuinSigil.Level - 1) * 0.02f;
    float AccuracyReduction = 0.3f + (EquippedRuinSigil.Level - 1) * 0.015f;

    ShadowDamageSpec.SetSetByCallerMagnitude(AuracronSigilTags::Sigil_Effect_Damage, BaseDamage);
    ShadowDamageSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Sigil.Effect.Blind")), VisionReduction);
    ShadowDamageSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Sigil.Effect.Accuracy")), AccuracyReduction);

    FActiveGameplayEffectHandle EffectHandle = AbilitySystemComponent->ApplyGameplayEffectSpecToSelf(ShadowDamageSpec);
    if (EffectHandle.IsValid())
    {
        ActiveRuinSigilEffects.Add(EffectHandle);

        // Apply shadow field that reduces enemy vision
        ApplyShadowFieldEffect(500.0f, VisionReduction, AccuracyReduction);

        // Grant stealth bonuses to user
        GrantStealthBonuses();

        // Spawn visual effects
        SpawnRuinSigilVFX(EAuracronRuinSigilType::Sombrio);
        PlayRuinSigilAudio(EAuracronRuinSigilType::Sombrio);

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Ruin Sombrio effect applied - Damage: %.1f, Vision: %.1f%%, Accuracy: %.1f%%"),
            BaseDamage, VisionReduction * 100.0f, AccuracyReduction * 100.0f);
        return true;
    }

    return false;
}

bool UAuracronSigilosBridge::ApplyCorrosivoRuinEffect()
{
    if (!AbilitySystemComponent)
    {
        return false;
    }

    // Create GameplayEffect for Ruin Corrosivo
    FGameplayEffectContextHandle EffectContext = AbilitySystemComponent->MakeEffectContext();
    EffectContext.AddSourceObject(this);
    EffectContext.AddActors({GetOwner()}, false);

    // Create corrosive damage and armor reduction effect spec using UE 5.6 API
    UGameplayEffect* CorrosiveEffect = GetRuinCorrosivoGameplayEffect().GetDefaultObject();
    FGameplayEffectSpec CorrosiveDamageSpec(CorrosiveEffect, EffectContext, EquippedRuinSigil.Level);

    // Apply corrosive damage parameters
    float BaseDamage = 60.0f * (1.0f + (EquippedRuinSigil.Level - 1) * 0.13f);
    float ArmorReduction = 0.4f + (EquippedRuinSigil.Level - 1) * 0.02f;
    float ResistanceReduction = 0.3f + (EquippedRuinSigil.Level - 1) * 0.015f;

    CorrosiveDamageSpec.SetSetByCallerMagnitude(AuracronSigilTags::Sigil_Effect_Damage, BaseDamage);
    CorrosiveDamageSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Sigil.Effect.ArmorReduction")), ArmorReduction);
    CorrosiveDamageSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Sigil.Effect.ResistanceReduction")), ResistanceReduction);

    FActiveGameplayEffectHandle EffectHandle = AbilitySystemComponent->ApplyGameplayEffectSpecToSelf(CorrosiveDamageSpec);
    if (EffectHandle.IsValid())
    {
        ActiveRuinSigilEffects.Add(EffectHandle);

        // Apply corrosive aura that weakens nearby enemies
        ApplyCorrosiveAuraEffect(450.0f, ArmorReduction, ResistanceReduction);

        // Grant armor penetration bonus to user
        GrantArmorPenetrationBonus(0.15f + (EquippedRuinSigil.Level - 1) * 0.01f);

        // Spawn visual effects
        SpawnRuinSigilVFX(EAuracronRuinSigilType::Corrosivo);
        PlayRuinSigilAudio(EAuracronRuinSigilType::Corrosivo);

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Ruin Corrosivo effect applied - Damage: %.1f, Armor Reduction: %.1f%%, Resistance: %.1f%%"),
            BaseDamage, ArmorReduction * 100.0f, ResistanceReduction * 100.0f);
        return true;
    }

    return false;
}

bool UAuracronSigilosBridge::ApplyAniquiladorRuinEffect()
{
    if (!AbilitySystemComponent)
    {
        return false;
    }

    // Create GameplayEffect for Ruin Aniquilador
    FGameplayEffectContextHandle EffectContext = AbilitySystemComponent->MakeEffectContext();
    EffectContext.AddSourceObject(this);
    EffectContext.AddActors({GetOwner()}, false);

    // Create massive damage and execution effect spec
    UGameplayEffect* AnnihilationEffect = GetRuinAniquiladorGameplayEffect().GetDefaultObject();
    FGameplayEffectSpec AnnihilationSpec(AnnihilationEffect, EffectContext, EquippedRuinSigil.Level);

    // Apply annihilation parameters
    float BaseDamage = 300.0f * (1.0f + (EquippedRuinSigil.Level - 1) * 0.2f);
    float ExecutionThreshold = 0.2f + (EquippedRuinSigil.Level - 1) * 0.01f;
    float CriticalMultiplier = 2.5f + (EquippedRuinSigil.Level - 1) * 0.1f;

    AnnihilationSpec.SetSetByCallerMagnitude(AuracronSigilTags::Sigil_Effect_Damage, BaseDamage);
    AnnihilationSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Sigil.Effect.Execution")), ExecutionThreshold);
    AnnihilationSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Sigil.Effect.Critical")), CriticalMultiplier);

    FActiveGameplayEffectHandle EffectHandle = AbilitySystemComponent->ApplyGameplayEffectSpecToSelf(AnnihilationSpec);
    if (EffectHandle.IsValid())
    {
        ActiveRuinSigilEffects.Add(EffectHandle);

        // Apply execution logic to nearby enemies
        ApplyExecutionEffect(600.0f, ExecutionThreshold);

        // Grant critical damage bonus
        GrantCriticalDamageBonus(0.2f + (EquippedRuinSigil.Level - 1) * 0.01f);

        // Spawn visual effects
        SpawnRuinSigilVFX(EAuracronRuinSigilType::Aniquilador);
        PlayRuinSigilAudio(EAuracronRuinSigilType::Aniquilador);

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Ruin Aniquilador effect applied - Damage: %.1f, Execution: %.1f%%, Critical: %.1fx"),
            BaseDamage, ExecutionThreshold * 100.0f, CriticalMultiplier);
        return true;
    }

    return false;
}

// === Specific Vesper Sigil Effect Implementations ===

bool UAuracronSigilosBridge::ApplyCurativoVesperEffect()
{
    if (!AbilitySystemComponent)
    {
        return false;
    }

    // Create GameplayEffect for Vesper Curativo
    FGameplayEffectContextHandle EffectContext = AbilitySystemComponent->MakeEffectContext();
    EffectContext.AddSourceObject(this);
    EffectContext.AddActors({GetOwner()}, false);

    // Create healing over time effect spec
    UGameplayEffect* HealingEffect = GetVesperCurativoGameplayEffect().GetDefaultObject();
    FGameplayEffectSpec HealingSpec(HealingEffect, EffectContext, EquippedVesperSigil.Level);

    // Apply healing parameters
    float HealingPerSecond = 30.0f * (1.0f + (EquippedVesperSigil.Level - 1) * 0.08f);
    float HealingRadius = 400.0f + (EquippedVesperSigil.Level - 1) * 15.0f;
    float OverhealPercentage = 0.2f + (EquippedVesperSigil.Level - 1) * 0.01f;

    HealingSpec.SetSetByCallerMagnitude(AuracronSigilTags::Sigil_Effect_Heal, HealingPerSecond);
    HealingSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Sigil.Effect.Radius")), HealingRadius);
    HealingSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Sigil.Effect.Overheal")), OverhealPercentage);

    FActiveGameplayEffectHandle EffectHandle = AbilitySystemComponent->ApplyGameplayEffectSpecToSelf(HealingSpec);
    if (EffectHandle.IsValid())
    {
        ActiveVesperSigilEffects.Add(EffectHandle);

        // Apply area healing effect to nearby allies
        ApplyAreaHealingEffect(HealingRadius, HealingPerSecond, OverhealPercentage);

        // Grant healing received bonus
        GrantHealingReceivedBonus(0.15f + (EquippedVesperSigil.Level - 1) * 0.01f);

        // Spawn visual effects
        SpawnVesperSigilVFX(EAuracronVesperSigilType::Curativo);
        PlayVesperSigilAudio(EAuracronVesperSigilType::Curativo);

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Vesper Curativo effect applied - Healing: %.1f/s, Radius: %.1f, Overheal: %.1f%%"),
            HealingPerSecond, HealingRadius, OverhealPercentage * 100.0f);
        return true;
    }

    return false;
}

bool UAuracronSigilosBridge::ApplyEnergeticoVesperEffect()
{
    if (!AbilitySystemComponent)
    {
        return false;
    }

    // Create GameplayEffect for Vesper Energético
    FGameplayEffectContextHandle EffectContext = AbilitySystemComponent->MakeEffectContext();
    EffectContext.AddSourceObject(this);
    EffectContext.AddActors({GetOwner()}, false);

    // Create mana regeneration and cost reduction effect spec
    UGameplayEffect* EnergyEffect = GetVesperEnergeticoGameplayEffect().GetDefaultObject();
    FGameplayEffectSpec EnergySpec(EnergyEffect, EffectContext, EquippedVesperSigil.Level);

    // Apply energy parameters
    float ManaPerSecond = 20.0f * (1.0f + (EquippedVesperSigil.Level - 1) * 0.06f);
    float CostReduction = 0.25f + (EquippedVesperSigil.Level - 1) * 0.01f;
    float EnergyEfficiency = 1.3f + (EquippedVesperSigil.Level - 1) * 0.05f;

    EnergySpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Sigil.Effect.ManaRegen")), ManaPerSecond);
    EnergySpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Sigil.Effect.CostReduction")), CostReduction);
    EnergySpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Sigil.Effect.Efficiency")), EnergyEfficiency);

    FActiveGameplayEffectHandle EffectHandle = AbilitySystemComponent->ApplyGameplayEffectSpecToSelf(EnergySpec);
    if (EffectHandle.IsValid())
    {
        ActiveVesperSigilEffects.Add(EffectHandle);

        // Apply energy field that benefits nearby allies
        ApplyEnergyFieldEffect(350.0f, ManaPerSecond * 0.5f, CostReduction * 0.5f);

        // Grant max mana bonus
        GrantMaxManaBonus(0.1f + (EquippedVesperSigil.Level - 1) * 0.005f);

        // Spawn visual effects
        SpawnVesperSigilVFX(EAuracronVesperSigilType::Energetico);
        PlayVesperSigilAudio(EAuracronVesperSigilType::Energetico);

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Vesper Energético effect applied - Mana: %.1f/s, Cost Reduction: %.1f%%, Efficiency: %.1fx"),
            ManaPerSecond, CostReduction * 100.0f, EnergyEfficiency);
        return true;
    }

    return false;
}

bool UAuracronSigilosBridge::ApplyVelocidadeVesperEffect()
{
    if (!AbilitySystemComponent)
    {
        return false;
    }

    // Create GameplayEffect for Vesper Velocidade
    FGameplayEffectContextHandle EffectContext = AbilitySystemComponent->MakeEffectContext();
    EffectContext.AddSourceObject(this);
    EffectContext.AddActors({GetOwner()}, false);

    // Create speed boost effect spec
    UGameplayEffect* SpeedEffect = GetVesperVelocidadeGameplayEffect().GetDefaultObject();
    FGameplayEffectSpec SpeedSpec(SpeedEffect, EffectContext, EquippedVesperSigil.Level);

    // Apply speed parameters
    float MovementSpeedBonus = 0.4f + (EquippedVesperSigil.Level - 1) * 0.02f;
    float AttackSpeedBonus = 0.3f + (EquippedVesperSigil.Level - 1) * 0.015f;
    int32 DashCharges = 2 + (EquippedVesperSigil.Level - 1) / 5; // Extra charge every 5 levels

    SpeedSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Sigil.Effect.MovementSpeed")), MovementSpeedBonus);
    SpeedSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Sigil.Effect.AttackSpeed")), AttackSpeedBonus);
    SpeedSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Sigil.Effect.DashCharges")), static_cast<float>(DashCharges));

    FActiveGameplayEffectHandle EffectHandle = AbilitySystemComponent->ApplyGameplayEffectSpecToSelf(SpeedSpec);
    if (EffectHandle.IsValid())
    {
        ActiveVesperSigilEffects.Add(EffectHandle);

        // Grant dash charges using UE 5.6 ability system
        GrantDashCharges(DashCharges);

        // Apply wind trail effect
        ApplyWindTrailEffect();

        // Spawn visual effects
        SpawnVesperSigilVFX(EAuracronVesperSigilType::Velocidade);
        PlayVesperSigilAudio(EAuracronVesperSigilType::Velocidade);

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Vesper Velocidade effect applied - Movement: %.1f%%, Attack: %.1f%%, Dashes: %d"),
            MovementSpeedBonus * 100.0f, AttackSpeedBonus * 100.0f, DashCharges);
        return true;
    }

    return false;
}

bool UAuracronSigilosBridge::ApplyVisaoVesperEffect()
{
    if (!AbilitySystemComponent)
    {
        return false;
    }

    // Create GameplayEffect for Vesper Visão
    FGameplayEffectContextHandle EffectContext = AbilitySystemComponent->MakeEffectContext();
    EffectContext.AddSourceObject(this);
    EffectContext.AddActors({GetOwner()}, false);

    // Create vision enhancement effect spec
    UGameplayEffect* VisionEffect = GetVesperVisaoGameplayEffect().GetDefaultObject();
    FGameplayEffectSpec VisionSpec(VisionEffect, EffectContext, EquippedVesperSigil.Level);

    // Apply vision parameters
    float VisionRangeBonus = 0.5f + (EquippedVesperSigil.Level - 1) * 0.03f;
    float TrueSightRadius = 600.0f + (EquippedVesperSigil.Level - 1) * 20.0f;
    float DetectionBonus = 0.8f + (EquippedVesperSigil.Level - 1) * 0.02f;

    VisionSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Sigil.Effect.VisionRange")), VisionRangeBonus);
    VisionSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Sigil.Effect.TrueSight")), TrueSightRadius);
    VisionSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Sigil.Effect.Detection")), DetectionBonus);

    FActiveGameplayEffectHandle EffectHandle = AbilitySystemComponent->ApplyGameplayEffectSpecToSelf(VisionSpec);
    if (EffectHandle.IsValid())
    {
        ActiveVesperSigilEffects.Add(EffectHandle);

        // Apply true sight effect to detect invisible enemies
        ApplyTrueSightEffect(TrueSightRadius);

        // Enhance vision range using UE 5.6 camera system
        EnhanceVisionRange(VisionRangeBonus);

        // Spawn visual effects
        SpawnVesperSigilVFX(EAuracronVesperSigilType::Visao);
        PlayVesperSigilAudio(EAuracronVesperSigilType::Visao);

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Vesper Visão effect applied - Vision: %.1f%%, True Sight: %.1f, Detection: %.1f%%"),
            VisionRangeBonus * 100.0f, TrueSightRadius, DetectionBonus * 100.0f);
        return true;
    }

    return false;
}

bool UAuracronSigilosBridge::ApplyTeleporteVesperEffect()
{
    if (!AbilitySystemComponent)
    {
        return false;
    }

    // Create GameplayEffect for Vesper Teleporte
    FGameplayEffectContextHandle EffectContext = AbilitySystemComponent->MakeEffectContext();
    EffectContext.AddSourceObject(this);
    EffectContext.AddActors({GetOwner()}, false);

    // Create teleportation effect spec
    UGameplayEffect* TeleportEffect = GetVesperTeleporteGameplayEffect().GetDefaultObject();
    FGameplayEffectSpec TeleportSpec(TeleportEffect, EffectContext, EquippedVesperSigil.Level);

    // Apply teleport parameters
    float TeleportRange = 800.0f + (EquippedVesperSigil.Level - 1) * 30.0f;
    int32 TeleportCharges = 3 + (EquippedVesperSigil.Level - 1) / 4; // Extra charge every 4 levels
    float ChargeCooldown = 8.0f - (EquippedVesperSigil.Level - 1) * 0.2f;
    float DodgeChance = 0.05f + (EquippedVesperSigil.Level - 1) * 0.005f;

    TeleportSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Sigil.Effect.TeleportRange")), TeleportRange);
    TeleportSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Sigil.Effect.TeleportCharges")), static_cast<float>(TeleportCharges));
    TeleportSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Sigil.Effect.ChargeCooldown")), ChargeCooldown);
    TeleportSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Sigil.Effect.DodgeChance")), DodgeChance);

    FActiveGameplayEffectHandle EffectHandle = AbilitySystemComponent->ApplyGameplayEffectSpecToSelf(TeleportSpec);
    if (EffectHandle.IsValid())
    {
        ActiveVesperSigilEffects.Add(EffectHandle);

        // Grant teleport charges using modern UE 5.6 ability system
        GrantTeleportCharges(TeleportCharges, TeleportRange, ChargeCooldown);

        // Apply dodge chance bonus
        GrantDodgeChanceBonus(DodgeChance);

        // Spawn visual effects
        SpawnVesperSigilVFX(EAuracronVesperSigilType::Teleporte);
        PlayVesperSigilAudio(EAuracronVesperSigilType::Teleporte);

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Vesper Teleporte effect applied - Range: %.1f, Charges: %d, Cooldown: %.1fs, Dodge: %.1f%%"),
            TeleportRange, TeleportCharges, ChargeCooldown, DodgeChance * 100.0f);
        return true;
    }

    return false;
}

bool UAuracronSigilosBridge::ApplyTemporalVesperEffect()
{
    if (!AbilitySystemComponent)
    {
        return false;
    }

    // Create GameplayEffect for Vesper Temporal
    FGameplayEffectContextHandle EffectContext = AbilitySystemComponent->MakeEffectContext();
    EffectContext.AddSourceObject(this);
    EffectContext.AddActors({GetOwner()}, false);

    // Create time manipulation effect spec
    UGameplayEffect* TemporalEffect = GetVesperTemporalGameplayEffect().GetDefaultObject();
    FGameplayEffectSpec TemporalSpec(TemporalEffect, EffectContext, EquippedVesperSigil.Level);

    // Apply temporal parameters
    float TimeAcceleration = 1.5f + (EquippedVesperSigil.Level - 1) * 0.05f;
    float TimeDeceleration = 0.6f - (EquippedVesperSigil.Level - 1) * 0.02f;
    float EffectRadius = 500.0f + (EquippedVesperSigil.Level - 1) * 20.0f;
    float CooldownRecovery = 0.1f + (EquippedVesperSigil.Level - 1) * 0.005f;

    TemporalSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Sigil.Effect.TimeAcceleration")), TimeAcceleration);
    TemporalSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Sigil.Effect.TimeDeceleration")), TimeDeceleration);
    TemporalSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Sigil.Effect.Radius")), EffectRadius);
    TemporalSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Sigil.Effect.CooldownRecovery")), CooldownRecovery);

    FActiveGameplayEffectHandle EffectHandle = AbilitySystemComponent->ApplyGameplayEffectSpecToSelf(TemporalSpec);
    if (EffectHandle.IsValid())
    {
        ActiveVesperSigilEffects.Add(EffectHandle);

        // Apply time manipulation field using UE 5.6 time dilation system
        ApplyTimeManipulationField(EffectRadius, TimeAcceleration, TimeDeceleration);

        // Grant cooldown recovery bonus
        GrantCooldownRecoveryBonus(CooldownRecovery);

        // Spawn visual effects
        SpawnVesperSigilVFX(EAuracronVesperSigilType::Temporal);
        PlayVesperSigilAudio(EAuracronVesperSigilType::Temporal);

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Vesper Temporal effect applied - Acceleration: %.1fx, Deceleration: %.1fx, Radius: %.1f, Recovery: %.1f%%"),
            TimeAcceleration, TimeDeceleration, EffectRadius, CooldownRecovery * 100.0f);
        return true;
    }

    return false;
}

// === GameplayEffect Getters Implementation ===

TSubclassOf<UGameplayEffect> UAuracronSigilosBridge::GetAegisPrimordialGameplayEffect() const
{
    // Load GameplayEffect using UE 5.6 soft object path resolution
    static const FSoftClassPath AegisPrimordialPath(TEXT("/Game/GameplayEffects/Sigils/Aegis/GE_AegisPrimordial.GE_AegisPrimordial_C"));
    return AegisPrimordialPath.TryLoadClass<UGameplayEffect>();
}

TSubclassOf<UGameplayEffect> UAuracronSigilosBridge::GetAegisCristalinoGameplayEffect() const
{
    static const FSoftClassPath AegisCristalinoPath(TEXT("/Game/GameplayEffects/Sigils/Aegis/GE_AegisCristalino.GE_AegisCristalino_C"));
    return AegisCristalinoPath.TryLoadClass<UGameplayEffect>();
}

TSubclassOf<UGameplayEffect> UAuracronSigilosBridge::GetAegisTemporalGameplayEffect() const
{
    static const FSoftClassPath AegisTemporalPath(TEXT("/Game/GameplayEffects/Sigils/Aegis/GE_AegisTemporal.GE_AegisTemporal_C"));
    return AegisTemporalPath.TryLoadClass<UGameplayEffect>();
}

TSubclassOf<UGameplayEffect> UAuracronSigilosBridge::GetAegisEspectralGameplayEffect() const
{
    static const FSoftClassPath AegisEspectralPath(TEXT("/Game/GameplayEffects/Sigils/Aegis/GE_AegisEspectral.GE_AegisEspectral_C"));
    return AegisEspectralPath.TryLoadClass<UGameplayEffect>();
}

TSubclassOf<UGameplayEffect> UAuracronSigilosBridge::GetAegisAbsolutoGameplayEffect() const
{
    static const FSoftClassPath AegisAbsolutoPath(TEXT("/Game/GameplayEffects/Sigils/Aegis/GE_AegisAbsoluto.GE_AegisAbsoluto_C"));
    return AegisAbsolutoPath.TryLoadClass<UGameplayEffect>();
}

TSubclassOf<UGameplayEffect> UAuracronSigilosBridge::GetRuinFlamejanteGameplayEffect() const
{
    static const FSoftClassPath RuinFlamejantePath(TEXT("/Game/GameplayEffects/Sigils/Ruin/GE_RuinFlamejante.GE_RuinFlamejante_C"));
    return RuinFlamejantePath.TryLoadClass<UGameplayEffect>();
}

TSubclassOf<UGameplayEffect> UAuracronSigilosBridge::GetRuinGelidoGameplayEffect() const
{
    static const FSoftClassPath RuinGelidoPath(TEXT("/Game/GameplayEffects/Sigils/Ruin/GE_RuinGelido.GE_RuinGelido_C"));
    return RuinGelidoPath.TryLoadClass<UGameplayEffect>();
}

TSubclassOf<UGameplayEffect> UAuracronSigilosBridge::GetRuinSombrioGameplayEffect() const
{
    static const FSoftClassPath RuinSombrioPath(TEXT("/Game/GameplayEffects/Sigils/Ruin/GE_RuinSombrio.GE_RuinSombrio_C"));
    return RuinSombrioPath.TryLoadClass<UGameplayEffect>();
}

TSubclassOf<UGameplayEffect> UAuracronSigilosBridge::GetRuinCorrosivoGameplayEffect() const
{
    static const FSoftClassPath RuinCorrosivoPath(TEXT("/Game/GameplayEffects/Sigils/Ruin/GE_RuinCorrosivo.GE_RuinCorrosivo_C"));
    return RuinCorrosivoPath.TryLoadClass<UGameplayEffect>();
}

TSubclassOf<UGameplayEffect> UAuracronSigilosBridge::GetRuinAniquiladorGameplayEffect() const
{
    static const FSoftClassPath RuinAniquiladorPath(TEXT("/Game/GameplayEffects/Sigils/Ruin/GE_RuinAniquilador.GE_RuinAniquilador_C"));
    return RuinAniquiladorPath.TryLoadClass<UGameplayEffect>();
}

TSubclassOf<UGameplayEffect> UAuracronSigilosBridge::GetVesperCurativoGameplayEffect() const
{
    static const FSoftClassPath VesperCurativoPath(TEXT("/Game/GameplayEffects/Sigils/Vesper/GE_VesperCurativo.GE_VesperCurativo_C"));
    return VesperCurativoPath.TryLoadClass<UGameplayEffect>();
}

TSubclassOf<UGameplayEffect> UAuracronSigilosBridge::GetVesperEnergeticoGameplayEffect() const
{
    static const FSoftClassPath VesperEnergeticoPath(TEXT("/Game/GameplayEffects/Sigils/Vesper/GE_VesperEnergetico.GE_VesperEnergetico_C"));
    return VesperEnergeticoPath.TryLoadClass<UGameplayEffect>();
}

TSubclassOf<UGameplayEffect> UAuracronSigilosBridge::GetVesperVelocidadeGameplayEffect() const
{
    static const FSoftClassPath VesperVelocidadePath(TEXT("/Game/GameplayEffects/Sigils/Vesper/GE_VesperVelocidade.GE_VesperVelocidade_C"));
    return VesperVelocidadePath.TryLoadClass<UGameplayEffect>();
}

TSubclassOf<UGameplayEffect> UAuracronSigilosBridge::GetVesperVisaoGameplayEffect() const
{
    static const FSoftClassPath VesperVisaoPath(TEXT("/Game/GameplayEffects/Sigils/Vesper/GE_VesperVisao.GE_VesperVisao_C"));
    return VesperVisaoPath.TryLoadClass<UGameplayEffect>();
}

TSubclassOf<UGameplayEffect> UAuracronSigilosBridge::GetVesperTeleporteGameplayEffect() const
{
    static const FSoftClassPath VesperTeleportePath(TEXT("/Game/GameplayEffects/Sigils/Vesper/GE_VesperTeleporte.GE_VesperTeleporte_C"));
    return VesperTeleportePath.TryLoadClass<UGameplayEffect>();
}

TSubclassOf<UGameplayEffect> UAuracronSigilosBridge::GetVesperTemporalGameplayEffect() const
{
    static const FSoftClassPath VesperTemporalPath(TEXT("/Game/GameplayEffects/Sigils/Vesper/GE_VesperTemporal.GE_VesperTemporal_C"));
    return VesperTemporalPath.TryLoadClass<UGameplayEffect>();
}

// === Visual Effects Implementation using UE 5.6 Niagara APIs ===

void UAuracronSigilosBridge::SpawnAegisSigilVFX(EAuracronAegisSigilType AegisType)
{
    if (!GetOwner())
    {
        return;
    }

    // Get VFX system path based on Aegis type
    FSoftObjectPath VFXPath;
    FLinearColor PrimaryColor;
    FLinearColor SecondaryColor;

    switch (AegisType)
    {
        case EAuracronAegisSigilType::Primordial:
            VFXPath = FSoftObjectPath(TEXT("/Game/VFX/Sigils/Aegis/NS_AegisPrimordial.NS_AegisPrimordial"));
            PrimaryColor = FLinearColor(0.53f, 0.81f, 0.92f, 1.0f); // Light Blue
            SecondaryColor = FLinearColor(0.28f, 0.51f, 0.71f, 1.0f); // Steel Blue
            break;
        case EAuracronAegisSigilType::Cristalino:
            VFXPath = FSoftObjectPath(TEXT("/Game/VFX/Sigils/Aegis/NS_AegisCristalino.NS_AegisCristalino"));
            PrimaryColor = FLinearColor(1.0f, 0.84f, 0.0f, 1.0f); // Gold
            SecondaryColor = FLinearColor(1.0f, 0.65f, 0.0f, 1.0f); // Orange
            break;
        case EAuracronAegisSigilType::Temporal:
            VFXPath = FSoftObjectPath(TEXT("/Game/VFX/Sigils/Aegis/NS_AegisTemporal.NS_AegisTemporal"));
            PrimaryColor = FLinearColor(0.58f, 0.44f, 0.86f, 1.0f); // Medium Purple
            SecondaryColor = FLinearColor(0.54f, 0.17f, 0.89f, 1.0f); // Blue Violet
            break;
        case EAuracronAegisSigilType::Espectral:
            VFXPath = FSoftObjectPath(TEXT("/Game/VFX/Sigils/Aegis/NS_AegisEspectral.NS_AegisEspectral"));
            PrimaryColor = FLinearColor(0.0f, 1.0f, 1.0f, 1.0f); // Cyan
            SecondaryColor = FLinearColor(0.0f, 0.55f, 0.55f, 1.0f); // Dark Turquoise
            break;
        case EAuracronAegisSigilType::Absoluto:
            VFXPath = FSoftObjectPath(TEXT("/Game/VFX/Sigils/Aegis/NS_AegisAbsoluto.NS_AegisAbsoluto"));
            PrimaryColor = FLinearColor(1.0f, 0.84f, 0.0f, 1.0f); // Gold
            SecondaryColor = FLinearColor(1.0f, 1.0f, 1.0f, 1.0f); // White
            break;
        default:
            return;
    }

    // Load Niagara system using UE 5.6 async loading
    if (UNiagaraSystem* NiagaraSystem = Cast<UNiagaraSystem>(VFXPath.TryLoad()))
    {
        // Spawn Niagara system attached to owner using modern APIs
        UNiagaraComponent* NiagaraComponent = UNiagaraFunctionLibrary::SpawnSystemAttached(
            NiagaraSystem,
            GetOwner()->GetRootComponent(),
            NAME_None,
            FVector::ZeroVector,
            FRotator::ZeroRotator,
            EAttachLocation::KeepWorldPosition,
            true, // Auto destroy
            true, // Auto activate
            ENCPoolMethod::None,
            true  // Prewarm
        );

        if (NiagaraComponent)
        {
            // Set dynamic parameters using UE 5.6 Niagara APIs
            // Production Ready: UE 5.6 compatible Niagara API using FName
            NiagaraComponent->SetVariableLinearColor(FName("PrimaryColor"), PrimaryColor);
            NiagaraComponent->SetVariableLinearColor(FName("SecondaryColor"), SecondaryColor);
            NiagaraComponent->SetVariableFloat(FName("SigilLevel"), static_cast<float>(EquippedAegisSigil.Level));
            NiagaraComponent->SetVariableFloat(FName("EffectIntensity"), 1.0f + (EquippedAegisSigil.Level - 1) * 0.1f);

            // Store reference for cleanup
            ActiveAegisSigilVFX.Add(NiagaraComponent);

            UE_LOG(LogTemp, Log, TEXT("AURACRON: Aegis VFX spawned for type: %d"), (int32)AegisType);
        }
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Failed to load Aegis VFX for type: %d"), (int32)AegisType);
    }
}

void UAuracronSigilosBridge::SpawnRuinSigilVFX(EAuracronRuinSigilType RuinType)
{
    if (!GetOwner())
    {
        return;
    }

    // Get VFX system path based on Ruin type
    FSoftObjectPath VFXPath;
    FLinearColor PrimaryColor;
    FLinearColor SecondaryColor;

    switch (RuinType)
    {
        case EAuracronRuinSigilType::Flamejante:
            VFXPath = FSoftObjectPath(TEXT("/Game/VFX/Sigils/Ruin/NS_RuinFlamejante.NS_RuinFlamejante"));
            PrimaryColor = FLinearColor(1.0f, 0.27f, 0.0f, 1.0f); // Orange Red
            SecondaryColor = FLinearColor(1.0f, 0.39f, 0.28f, 1.0f); // Tomato
            break;
        case EAuracronRuinSigilType::Gelido:
            VFXPath = FSoftObjectPath(TEXT("/Game/VFX/Sigils/Ruin/NS_RuinGelido.NS_RuinGelido"));
            PrimaryColor = FLinearColor(0.0f, 0.75f, 1.0f, 1.0f); // Deep Sky Blue
            SecondaryColor = FLinearColor(0.53f, 0.81f, 0.98f, 1.0f); // Light Sky Blue
            break;
        case EAuracronRuinSigilType::Sombrio:
            VFXPath = FSoftObjectPath(TEXT("/Game/VFX/Sigils/Ruin/NS_RuinSombrio.NS_RuinSombrio"));
            PrimaryColor = FLinearColor(0.18f, 0.18f, 0.18f, 1.0f); // Dark Gray
            SecondaryColor = FLinearColor(0.5f, 0.0f, 0.5f, 1.0f); // Purple
            break;
        case EAuracronRuinSigilType::Corrosivo:
            VFXPath = FSoftObjectPath(TEXT("/Game/VFX/Sigils/Ruin/NS_RuinCorrosivo.NS_RuinCorrosivo"));
            PrimaryColor = FLinearColor(0.2f, 0.8f, 0.2f, 1.0f); // Lime Green
            SecondaryColor = FLinearColor(0.13f, 0.55f, 0.13f, 1.0f); // Forest Green
            break;
        case EAuracronRuinSigilType::Aniquilador:
            VFXPath = FSoftObjectPath(TEXT("/Game/VFX/Sigils/Ruin/NS_RuinAniquilador.NS_RuinAniquilador"));
            PrimaryColor = FLinearColor(0.55f, 0.0f, 0.0f, 1.0f); // Dark Red
            SecondaryColor = FLinearColor(0.0f, 0.0f, 0.0f, 1.0f); // Black
            break;
        default:
            return;
    }

    // Load and spawn Niagara system
    if (UNiagaraSystem* NiagaraSystem = Cast<UNiagaraSystem>(VFXPath.TryLoad()))
    {
        UNiagaraComponent* NiagaraComponent = UNiagaraFunctionLibrary::SpawnSystemAttached(
            NiagaraSystem,
            GetOwner()->GetRootComponent(),
            NAME_None,
            FVector::ZeroVector,
            FRotator::ZeroRotator,
            EAttachLocation::KeepWorldPosition,
            true, // Auto destroy
            true, // Auto activate
            ENCPoolMethod::AutoRelease,
            true  // Prewarm
        );

        if (NiagaraComponent)
        {
            // Set dynamic parameters
            // Production Ready: UE 5.6 compatible Niagara API
            NiagaraComponent->SetVariableLinearColor(FName("PrimaryColor"), PrimaryColor);
            NiagaraComponent->SetVariableLinearColor(FName("SecondaryColor"), SecondaryColor);
            NiagaraComponent->SetVariableFloat(FName("SigilLevel"), static_cast<float>(EquippedRuinSigil.Level));
            NiagaraComponent->SetVariableFloat(FName("DamageIntensity"), 1.0f + (EquippedRuinSigil.Level - 1) * 0.15f);

            // Store reference
            ActiveRuinSigilVFX.Add(NiagaraComponent);

            UE_LOG(LogTemp, Log, TEXT("AURACRON: Ruin VFX spawned for type: %d"), (int32)RuinType);
        }
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Failed to load Ruin VFX for type: %d"), (int32)RuinType);
    }
}

void UAuracronSigilosBridge::SpawnVesperSigilVFX(EAuracronVesperSigilType VesperType)
{
    if (!GetOwner())
    {
        return;
    }

    // Get VFX system path based on Vesper type
    FSoftObjectPath VFXPath;
    FLinearColor PrimaryColor;
    FLinearColor SecondaryColor;

    switch (VesperType)
    {
        case EAuracronVesperSigilType::Curativo:
            VFXPath = FSoftObjectPath(TEXT("/Game/VFX/Sigils/Vesper/NS_VesperCurativo.NS_VesperCurativo"));
            PrimaryColor = FLinearColor(0.0f, 1.0f, 0.5f, 1.0f); // Spring Green
            SecondaryColor = FLinearColor(0.6f, 0.98f, 0.6f, 1.0f); // Pale Green
            break;
        case EAuracronVesperSigilType::Energetico:
            VFXPath = FSoftObjectPath(TEXT("/Game/VFX/Sigils/Vesper/NS_VesperEnergetico.NS_VesperEnergetico"));
            PrimaryColor = FLinearColor(0.12f, 0.56f, 1.0f, 1.0f); // Dodger Blue
            SecondaryColor = FLinearColor(0.53f, 0.81f, 0.92f, 1.0f); // Light Blue
            break;
        case EAuracronVesperSigilType::Velocidade:
            VFXPath = FSoftObjectPath(TEXT("/Game/VFX/Sigils/Vesper/NS_VesperVelocidade.NS_VesperVelocidade"));
            PrimaryColor = FLinearColor(1.0f, 1.0f, 0.0f, 1.0f); // Yellow
            SecondaryColor = FLinearColor(1.0f, 0.84f, 0.0f, 1.0f); // Gold
            break;
        case EAuracronVesperSigilType::Visao:
            VFXPath = FSoftObjectPath(TEXT("/Game/VFX/Sigils/Vesper/NS_VesperVisao.NS_VesperVisao"));
            PrimaryColor = FLinearColor(1.0f, 1.0f, 1.0f, 1.0f); // White
            SecondaryColor = FLinearColor(0.94f, 0.97f, 1.0f, 1.0f); // Alice Blue
            break;
        case EAuracronVesperSigilType::Teleporte:
            VFXPath = FSoftObjectPath(TEXT("/Game/VFX/Sigils/Vesper/NS_VesperTeleporte.NS_VesperTeleporte"));
            PrimaryColor = FLinearColor(1.0f, 0.0f, 1.0f, 1.0f); // Magenta
            SecondaryColor = FLinearColor(0.85f, 0.44f, 0.84f, 1.0f); // Plum
            break;
        case EAuracronVesperSigilType::Temporal:
            VFXPath = FSoftObjectPath(TEXT("/Game/VFX/Sigils/Vesper/NS_VesperTemporal.NS_VesperTemporal"));
            PrimaryColor = FLinearColor(0.58f, 0.44f, 0.86f, 1.0f); // Medium Purple
            SecondaryColor = FLinearColor(0.87f, 0.63f, 0.87f, 1.0f); // Plum
            break;
        default:
            return;
    }

    // Load and spawn Niagara system
    if (UNiagaraSystem* NiagaraSystem = Cast<UNiagaraSystem>(VFXPath.TryLoad()))
    {
        UNiagaraComponent* NiagaraComponent = UNiagaraFunctionLibrary::SpawnSystemAttached(
            NiagaraSystem,
            GetOwner()->GetRootComponent(),
            NAME_None,
            FVector::ZeroVector,
            FRotator::ZeroRotator,
            EAttachLocation::KeepWorldPosition,
            true, // Auto destroy
            true, // Auto activate
            ENCPoolMethod::AutoRelease,
            true  // Prewarm
        );

        if (NiagaraComponent)
        {
            // Set dynamic parameters using UE 5.6 APIs
            // Production Ready: UE 5.6 compatible Niagara API
            NiagaraComponent->SetVariableLinearColor(FName("PrimaryColor"), PrimaryColor);
            NiagaraComponent->SetVariableLinearColor(FName("SecondaryColor"), SecondaryColor);
            NiagaraComponent->SetVariableFloat(FName("SigilLevel"), static_cast<float>(EquippedVesperSigil.Level));
            NiagaraComponent->SetVariableFloat(FName("SupportIntensity"), 1.0f + (EquippedVesperSigil.Level - 1) * 0.08f);

            // Set utility-specific parameters
            switch (VesperType)
            {
                case EAuracronVesperSigilType::Curativo:
                    NiagaraComponent->SetVariableFloat(FName("HealingRadius"), 400.0f + (EquippedVesperSigil.Level - 1) * 15.0f);
                    break;
                case EAuracronVesperSigilType::Velocidade:
                    NiagaraComponent->SetVariableFloat(FName("SpeedMultiplier"), 1.4f + (EquippedVesperSigil.Level - 1) * 0.02f);
                    break;
                case EAuracronVesperSigilType::Teleporte:
                    NiagaraComponent->SetVariableFloat(FName("TeleportRange"), 800.0f + (EquippedVesperSigil.Level - 1) * 30.0f);
                    break;
                case EAuracronVesperSigilType::Temporal:
                    NiagaraComponent->SetVariableFloat(FName("TimeDistortion"), 1.5f + (EquippedVesperSigil.Level - 1) * 0.05f);
                    break;
                default:
                    break;
            }

            // Store reference
            ActiveVesperSigilVFX.Add(NiagaraComponent);

            UE_LOG(LogTemp, Log, TEXT("AURACRON: Vesper VFX spawned for type: %d"), (int32)VesperType);
        }
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Failed to load Vesper VFX for type: %d"), (int32)VesperType);
    }
}

// === Audio Effects Implementation using UE 5.6 MetaSound APIs ===

void UAuracronSigilosBridge::PlayAegisSigilAudio(EAuracronAegisSigilType AegisType)
{
    if (!GetOwner())
    {
        return;
    }

    // Get MetaSound path based on Aegis type
    FSoftObjectPath AudioPath;
    float Volume = 0.8f;
    float Pitch = 1.0f;

    switch (AegisType)
    {
        case EAuracronAegisSigilType::Primordial:
            AudioPath = FSoftObjectPath(TEXT("/Game/Audio/Sigils/Aegis/MS_AegisPrimordial.MS_AegisPrimordial"));
            Volume = 0.8f;
            Pitch = 1.0f;
            break;
        case EAuracronAegisSigilType::Cristalino:
            AudioPath = FSoftObjectPath(TEXT("/Game/Audio/Sigils/Aegis/MS_AegisCristalino.MS_AegisCristalino"));
            Volume = 0.9f;
            Pitch = 1.1f;
            break;
        case EAuracronAegisSigilType::Temporal:
            AudioPath = FSoftObjectPath(TEXT("/Game/Audio/Sigils/Aegis/MS_AegisTemporal.MS_AegisTemporal"));
            Volume = 0.7f;
            Pitch = 0.8f;
            break;
        case EAuracronAegisSigilType::Espectral:
            AudioPath = FSoftObjectPath(TEXT("/Game/Audio/Sigils/Aegis/MS_AegisEspectral.MS_AegisEspectral"));
            Volume = 0.6f;
            Pitch = 1.3f;
            break;
        case EAuracronAegisSigilType::Absoluto:
            AudioPath = FSoftObjectPath(TEXT("/Game/Audio/Sigils/Aegis/MS_AegisAbsoluto.MS_AegisAbsoluto"));
            Volume = 1.0f;
            Pitch = 0.9f;
            break;
        default:
            return;
    }

    // Load MetaSound using UE 5.6 APIs
    if (UMetaSoundSource* MetaSound = Cast<UMetaSoundSource>(AudioPath.TryLoad()))
    {
        // Create audio component using modern UE 5.6 audio system
        UAudioComponent* AudioComponent = UGameplayStatics::SpawnSoundAttached(
            MetaSound,
            GetOwner()->GetRootComponent(),
            NAME_None,
            FVector::ZeroVector,
            FRotator::ZeroRotator,
            EAttachLocation::KeepWorldPosition,
            false, // Stop when owner destroyed
            Volume,
            Pitch,
            0.0f, // Start time
            nullptr, // Attenuation settings (use MetaSound's)
            nullptr, // Concurrency settings
            true     // Auto destroy
        );

        if (AudioComponent)
        {
            // Set MetaSound parameters using UE 5.6 APIs
            AudioComponent->SetFloatParameter(TEXT("SigilLevel"), static_cast<float>(EquippedAegisSigil.Level));
            AudioComponent->SetFloatParameter(TEXT("EffectIntensity"), 1.0f + (EquippedAegisSigil.Level - 1) * 0.1f);
            AudioComponent->SetBoolParameter(TEXT("IsDefensive"), true);

            // Store reference
            ActiveAegisSigilAudio.Add(AudioComponent);

            UE_LOG(LogTemp, Log, TEXT("AURACRON: Aegis Audio played for type: %d"), (int32)AegisType);
        }
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Failed to load Aegis Audio for type: %d"), (int32)AegisType);
    }
}

void UAuracronSigilosBridge::PlayRuinSigilAudio(EAuracronRuinSigilType RuinType)
{
    if (!GetOwner())
    {
        return;
    }

    // Get MetaSound path based on Ruin type
    FSoftObjectPath AudioPath;
    float Volume = 0.9f;
    float Pitch = 1.1f;

    switch (RuinType)
    {
        case EAuracronRuinSigilType::Flamejante:
            AudioPath = FSoftObjectPath(TEXT("/Game/Audio/Sigils/Ruin/MS_RuinFlamejante.MS_RuinFlamejante"));
            Volume = 0.9f;
            Pitch = 1.1f;
            break;
        case EAuracronRuinSigilType::Gelido:
            AudioPath = FSoftObjectPath(TEXT("/Game/Audio/Sigils/Ruin/MS_RuinGelido.MS_RuinGelido"));
            Volume = 0.8f;
            Pitch = 0.9f;
            break;
        case EAuracronRuinSigilType::Sombrio:
            AudioPath = FSoftObjectPath(TEXT("/Game/Audio/Sigils/Ruin/MS_RuinSombrio.MS_RuinSombrio"));
            Volume = 0.7f;
            Pitch = 0.7f;
            break;
        case EAuracronRuinSigilType::Corrosivo:
            AudioPath = FSoftObjectPath(TEXT("/Game/Audio/Sigils/Ruin/MS_RuinCorrosivo.MS_RuinCorrosivo"));
            Volume = 0.8f;
            Pitch = 1.2f;
            break;
        case EAuracronRuinSigilType::Aniquilador:
            AudioPath = FSoftObjectPath(TEXT("/Game/Audio/Sigils/Ruin/MS_RuinAniquilador.MS_RuinAniquilador"));
            Volume = 1.0f;
            Pitch = 0.6f;
            break;
        default:
            return;
    }

    // Load and play MetaSound
    if (UMetaSoundSource* MetaSound = Cast<UMetaSoundSource>(AudioPath.TryLoad()))
    {
        UAudioComponent* AudioComponent = UGameplayStatics::SpawnSoundAttached(
            MetaSound,
            GetOwner()->GetRootComponent(),
            NAME_None,
            FVector::ZeroVector,
            FRotator::ZeroRotator,
            EAttachLocation::KeepWorldPosition,
            false, // Stop when owner destroyed
            Volume,
            Pitch,
            0.0f, // Start time
            nullptr, // Attenuation settings
            nullptr, // Concurrency settings
            true     // Auto destroy
        );

        if (AudioComponent)
        {
            // Set MetaSound parameters
            AudioComponent->SetFloatParameter(TEXT("SigilLevel"), static_cast<float>(EquippedRuinSigil.Level));
            AudioComponent->SetFloatParameter(TEXT("DamageIntensity"), 1.0f + (EquippedRuinSigil.Level - 1) * 0.15f);
            AudioComponent->SetBoolParameter(TEXT("IsOffensive"), true);

            // Set element-specific parameters
            switch (RuinType)
            {
                case EAuracronRuinSigilType::Flamejante:
                    AudioComponent->SetStringParameter(TEXT("Element"), TEXT("Fire"));
                    break;
                case EAuracronRuinSigilType::Gelido:
                    AudioComponent->SetStringParameter(TEXT("Element"), TEXT("Ice"));
                    break;
                case EAuracronRuinSigilType::Sombrio:
                    AudioComponent->SetStringParameter(TEXT("Element"), TEXT("Shadow"));
                    break;
                case EAuracronRuinSigilType::Corrosivo:
                    AudioComponent->SetStringParameter(TEXT("Element"), TEXT("Acid"));
                    break;
                case EAuracronRuinSigilType::Aniquilador:
                    AudioComponent->SetStringParameter(TEXT("Element"), TEXT("Void"));
                    break;
                default:
                    break;
            }

            // Store reference
            ActiveRuinSigilAudio.Add(AudioComponent);

            UE_LOG(LogTemp, Log, TEXT("AURACRON: Ruin Audio played for type: %d"), (int32)RuinType);
        }
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Failed to load Ruin Audio for type: %d"), (int32)RuinType);
    }
}

void UAuracronSigilosBridge::PlayVesperSigilAudio(EAuracronVesperSigilType VesperType)
{
    if (!GetOwner())
    {
        return;
    }

    // Get MetaSound path based on Vesper type
    FSoftObjectPath AudioPath;
    float Volume = 0.7f;
    float Pitch = 1.2f;

    switch (VesperType)
    {
        case EAuracronVesperSigilType::Curativo:
            AudioPath = FSoftObjectPath(TEXT("/Game/Audio/Sigils/Vesper/MS_VesperCurativo.MS_VesperCurativo"));
            Volume = 0.7f;
            Pitch = 1.2f;
            break;
        case EAuracronVesperSigilType::Energetico:
            AudioPath = FSoftObjectPath(TEXT("/Game/Audio/Sigils/Vesper/MS_VesperEnergetico.MS_VesperEnergetico"));
            Volume = 0.6f;
            Pitch = 1.3f;
            break;
        case EAuracronVesperSigilType::Velocidade:
            AudioPath = FSoftObjectPath(TEXT("/Game/Audio/Sigils/Vesper/MS_VesperVelocidade.MS_VesperVelocidade"));
            Volume = 0.8f;
            Pitch = 1.4f;
            break;
        case EAuracronVesperSigilType::Visao:
            AudioPath = FSoftObjectPath(TEXT("/Game/Audio/Sigils/Vesper/MS_VesperVisao.MS_VesperVisao"));
            Volume = 0.5f;
            Pitch = 1.5f;
            break;
        case EAuracronVesperSigilType::Teleporte:
            AudioPath = FSoftObjectPath(TEXT("/Game/Audio/Sigils/Vesper/MS_VesperTeleporte.MS_VesperTeleporte"));
            Volume = 0.9f;
            Pitch = 1.6f;
            break;
        case EAuracronVesperSigilType::Temporal:
            AudioPath = FSoftObjectPath(TEXT("/Game/Audio/Sigils/Vesper/MS_VesperTemporal.MS_VesperTemporal"));
            Volume = 0.8f;
            Pitch = 0.8f;
            break;
        default:
            return;
    }

    // Load and play MetaSound
    if (UMetaSoundSource* MetaSound = Cast<UMetaSoundSource>(AudioPath.TryLoad()))
    {
        UAudioComponent* AudioComponent = UGameplayStatics::SpawnSoundAttached(
            MetaSound,
            GetOwner()->GetRootComponent(),
            NAME_None,
            FVector::ZeroVector,
            FRotator::ZeroRotator,
            EAttachLocation::KeepWorldPosition,
            false, // Stop when owner destroyed
            Volume,
            Pitch,
            0.0f, // Start time
            nullptr, // Attenuation settings
            nullptr, // Concurrency settings
            true     // Auto destroy
        );

        if (AudioComponent)
        {
            // Set MetaSound parameters using UE 5.6 APIs
            AudioComponent->SetFloatParameter(TEXT("SigilLevel"), static_cast<float>(EquippedVesperSigil.Level));
            AudioComponent->SetFloatParameter(TEXT("SupportIntensity"), 1.0f + (EquippedVesperSigil.Level - 1) * 0.08f);
            AudioComponent->SetBoolParameter(TEXT("IsSupport"), true);

            // Set utility-specific parameters
            switch (VesperType)
            {
                case EAuracronVesperSigilType::Curativo:
                    AudioComponent->SetStringParameter(TEXT("UtilityType"), TEXT("Healing"));
                    AudioComponent->SetFloatParameter(TEXT("HealingPower"), 30.0f + (EquippedVesperSigil.Level - 1) * 2.4f);
                    break;
                case EAuracronVesperSigilType::Energetico:
                    AudioComponent->SetStringParameter(TEXT("UtilityType"), TEXT("Energy"));
                    AudioComponent->SetFloatParameter(TEXT("EnergyPower"), 20.0f + (EquippedVesperSigil.Level - 1) * 1.2f);
                    break;
                case EAuracronVesperSigilType::Velocidade:
                    AudioComponent->SetStringParameter(TEXT("UtilityType"), TEXT("Speed"));
                    AudioComponent->SetFloatParameter(TEXT("SpeedBonus"), 0.4f + (EquippedVesperSigil.Level - 1) * 0.02f);
                    break;
                case EAuracronVesperSigilType::Visao:
                    AudioComponent->SetStringParameter(TEXT("UtilityType"), TEXT("Vision"));
                    AudioComponent->SetFloatParameter(TEXT("VisionRange"), 0.5f + (EquippedVesperSigil.Level - 1) * 0.03f);
                    break;
                case EAuracronVesperSigilType::Teleporte:
                    AudioComponent->SetStringParameter(TEXT("UtilityType"), TEXT("Teleport"));
                    AudioComponent->SetFloatParameter(TEXT("TeleportRange"), 800.0f + (EquippedVesperSigil.Level - 1) * 30.0f);
                    break;
                case EAuracronVesperSigilType::Temporal:
                    AudioComponent->SetStringParameter(TEXT("UtilityType"), TEXT("Time"));
                    AudioComponent->SetFloatParameter(TEXT("TimeDistortion"), 1.5f + (EquippedVesperSigil.Level - 1) * 0.05f);
                    break;
                default:
                    break;
            }

            // Store reference
            ActiveVesperSigilAudio.Add(AudioComponent);

            UE_LOG(LogTemp, Log, TEXT("AURACRON: Vesper Audio played for type: %d"), (int32)VesperType);
        }
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Failed to load Vesper Audio for type: %d"), (int32)VesperType);
    }
}

// === Archetype Property Calculation Methods ===

float UAuracronSigilosBridge::CalculateArchetypePowerMultiplier() const
{
    // Base power multiplier
    float BasePower = 1.0f;

    // Aegis contribution (30% weight)
    float AegisContribution = 0.3f;
    switch (EquippedAegisSigil.AegisSubtype)
    {
        case EAuracronAegisSigilType::Primordial:
            AegisContribution *= 1.0f; // Base
            break;
        case EAuracronAegisSigilType::Cristalino:
            AegisContribution *= 1.2f; // Reflection bonus
            break;
        case EAuracronAegisSigilType::Temporal:
            AegisContribution *= 1.3f; // Time manipulation bonus
            break;
        case EAuracronAegisSigilType::Espectral:
            AegisContribution *= 1.4f; // Magic absorption bonus
            break;
        case EAuracronAegisSigilType::Absoluto:
            AegisContribution *= 2.0f; // Legendary tier
            break;
        default:
            AegisContribution = 0.0f;
            break;
    }

    // Ruin contribution (40% weight - highest because it's damage focused)
    float RuinContribution = 0.4f;
    switch (EquippedRuinSigil.RuinSubtype)
    {
        case EAuracronRuinSigilType::Flamejante:
            RuinContribution *= 1.1f; // DoT bonus
            break;
        case EAuracronRuinSigilType::Gelido:
            RuinContribution *= 1.0f; // Base
            break;
        case EAuracronRuinSigilType::Sombrio:
            RuinContribution *= 1.3f; // Stealth bonus
            break;
        case EAuracronRuinSigilType::Corrosivo:
            RuinContribution *= 1.2f; // Armor penetration bonus
            break;
        case EAuracronRuinSigilType::Aniquilador:
            RuinContribution *= 2.5f; // Legendary tier
            break;
        default:
            RuinContribution = 0.0f;
            break;
    }

    // Vesper contribution (30% weight)
    float VesperContribution = 0.3f;
    switch (EquippedVesperSigil.VesperSubtype)
    {
        case EAuracronVesperSigilType::Curativo:
            VesperContribution *= 1.1f; // Healing bonus
            break;
        case EAuracronVesperSigilType::Energetico:
            VesperContribution *= 1.0f; // Base
            break;
        case EAuracronVesperSigilType::Velocidade:
            VesperContribution *= 1.2f; // Mobility bonus
            break;
        case EAuracronVesperSigilType::Visao:
            VesperContribution *= 1.1f; // Utility bonus
            break;
        case EAuracronVesperSigilType::Teleporte:
            VesperContribution *= 1.4f; // High mobility bonus
            break;
        case EAuracronVesperSigilType::Temporal:
            VesperContribution *= 1.8f; // Time manipulation bonus
            break;
        default:
            VesperContribution = 0.0f;
            break;
    }

    // Calculate synergy bonus based on elemental interactions
    float SynergyBonus = CalculateSynergyBonus();

    // Calculate level scaling bonus
    float AverageLevel = (EquippedAegisSigil.Level + EquippedRuinSigil.Level + EquippedVesperSigil.Level) / 3.0f;
    float LevelBonus = 1.0f + (AverageLevel - 1.0f) * 0.05f; // 5% per average level

    // Final calculation
    float FinalPower = BasePower + AegisContribution + RuinContribution + VesperContribution;
    FinalPower *= (1.0f + SynergyBonus);
    FinalPower *= LevelBonus;

    // Clamp to reasonable bounds
    return FMath::Clamp(FinalPower, 1.0f, 5.0f);
}

float UAuracronSigilosBridge::CalculateArchetypeCooldownReduction() const
{
    // Base cooldown reduction
    float BaseCooldownReduction = 0.0f;

    // Aegis cooldown benefits
    switch (EquippedAegisSigil.AegisSubtype)
    {
        case EAuracronAegisSigilType::Temporal:
            BaseCooldownReduction += 0.15f; // Time manipulation reduces cooldowns
            break;
        case EAuracronAegisSigilType::Absoluto:
            BaseCooldownReduction += 0.25f; // Legendary tier bonus
            break;
        default:
            BaseCooldownReduction += 0.05f; // Base defensive bonus
            break;
    }

    // Vesper cooldown benefits (support focused)
    switch (EquippedVesperSigil.VesperSubtype)
    {
        case EAuracronVesperSigilType::Energetico:
            BaseCooldownReduction += 0.2f; // Energy efficiency
            break;
        case EAuracronVesperSigilType::Temporal:
            BaseCooldownReduction += 0.3f; // Time manipulation mastery
            break;
        default:
            BaseCooldownReduction += 0.1f; // Base support bonus
            break;
    }

    // Level scaling
    float AverageLevel = (EquippedAegisSigil.Level + EquippedRuinSigil.Level + EquippedVesperSigil.Level) / 3.0f;
    float LevelBonus = (AverageLevel - 1.0f) * 0.01f; // 1% per average level

    // Synergy bonus
    float SynergyBonus = CalculateSynergyBonus() * 0.5f; // Half synergy applies to cooldown

    float FinalReduction = BaseCooldownReduction + LevelBonus + SynergyBonus;

    // Clamp to reasonable bounds (max 60% cooldown reduction)
    return FMath::Clamp(FinalReduction, 0.0f, 0.6f);
}

float UAuracronSigilosBridge::CalculateArchetypeEnergyEfficiency() const
{
    // Base energy efficiency
    float BaseEfficiency = 1.0f;

    // Vesper energy benefits (primary contributor)
    switch (EquippedVesperSigil.VesperSubtype)
    {
        case EAuracronVesperSigilType::Energetico:
            BaseEfficiency += 0.5f; // Major energy efficiency
            break;
        case EAuracronVesperSigilType::Temporal:
            BaseEfficiency += 0.3f; // Time efficiency
            break;
        default:
            BaseEfficiency += 0.1f; // Base support efficiency
            break;
    }

    // Aegis energy benefits
    switch (EquippedAegisSigil.AegisSubtype)
    {
        case EAuracronAegisSigilType::Espectral:
            BaseEfficiency += 0.2f; // Magic absorption efficiency
            break;
        case EAuracronAegisSigilType::Absoluto:
            BaseEfficiency += 0.4f; // Legendary efficiency
            break;
        default:
            BaseEfficiency += 0.05f; // Minimal defensive efficiency
            break;
    }

    // Level scaling
    float AverageLevel = (EquippedAegisSigil.Level + EquippedRuinSigil.Level + EquippedVesperSigil.Level) / 3.0f;
    float LevelBonus = (AverageLevel - 1.0f) * 0.03f; // 3% per average level

    // Synergy bonus
    float SynergyBonus = CalculateSynergyBonus() * 0.3f; // 30% of synergy applies to efficiency

    float FinalEfficiency = BaseEfficiency + LevelBonus + SynergyBonus;

    // Clamp to reasonable bounds
    return FMath::Clamp(FinalEfficiency, 1.0f, 3.0f);
}

float UAuracronSigilosBridge::CalculateSynergyBonus() const
{
    float SynergyBonus = 0.0f;

    // Elemental synergies using UE 5.6 modern enum comparisons
    bool bHasFireElement = (EquippedRuinSigil.RuinSubtype == EAuracronRuinSigilType::Flamejante);
    bool bHasIceElement = (EquippedRuinSigil.RuinSubtype == EAuracronRuinSigilType::Gelido);
    bool bHasShadowElement = (EquippedRuinSigil.RuinSubtype == EAuracronRuinSigilType::Sombrio);
    bool bHasCrystalElement = (EquippedAegisSigil.AegisSubtype == EAuracronAegisSigilType::Cristalino ||
                              EquippedAegisSigil.AegisSubtype == EAuracronAegisSigilType::Primordial);
    bool bHasTimeElement = (EquippedAegisSigil.AegisSubtype == EAuracronAegisSigilType::Temporal ||
                           EquippedVesperSigil.VesperSubtype == EAuracronVesperSigilType::Temporal);
    bool bHasLifeElement = (EquippedVesperSigil.VesperSubtype == EAuracronVesperSigilType::Curativo);
    bool bHasEnergyElement = (EquippedVesperSigil.VesperSubtype == EAuracronVesperSigilType::Energetico);
    bool bHasSpaceElement = (EquippedVesperSigil.VesperSubtype == EAuracronVesperSigilType::Teleporte);

    // Crystal resonance synergies
    if (bHasCrystalElement && (EquippedAegisSigil.AegisSubtype == EAuracronAegisSigilType::Cristalino))
    {
        SynergyBonus += 0.15f; // Crystal resonance
    }

    // Temporal mastery synergies
    if (bHasTimeElement && (EquippedAegisSigil.AegisSubtype == EAuracronAegisSigilType::Temporal &&
                           EquippedVesperSigil.VesperSubtype == EAuracronVesperSigilType::Temporal))
    {
        SynergyBonus += 0.25f; // Temporal mastery
    }

    // Life-Energy synergy
    if (bHasLifeElement && bHasEnergyElement)
    {
        SynergyBonus += 0.2f; // Life and energy complement each other
    }

    // Shadow-Space synergy (assassin combination)
    if (bHasShadowElement && bHasSpaceElement)
    {
        SynergyBonus += 0.22f; // Shadow teleportation mastery
    }

    // Fire-Ice opposition (reduced synergy)
    if (bHasFireElement && bHasIceElement)
    {
        SynergyBonus -= 0.1f; // Opposing elements
    }

    // Divine combinations (Absoluto + high-tier others)
    if (EquippedAegisSigil.AegisSubtype == EAuracronAegisSigilType::Absoluto)
    {
        if (EquippedRuinSigil.RuinSubtype == EAuracronRuinSigilType::Aniquilador)
        {
            SynergyBonus += 0.5f; // Ultimate destruction combination
        }
        if (EquippedVesperSigil.VesperSubtype == EAuracronVesperSigilType::Temporal)
        {
            SynergyBonus += 0.4f; // Divine time control
        }
    }

    // Level-based synergy enhancement
    float AverageLevel = (EquippedAegisSigil.Level + EquippedRuinSigil.Level + EquippedVesperSigil.Level) / 3.0f;
    float LevelMultiplier = 1.0f + (AverageLevel - 1.0f) * 0.02f; // 2% per level

    return FMath::Clamp(SynergyBonus * LevelMultiplier, -0.2f, 0.8f);
}

FString UAuracronSigilosBridge::GetAegisSigilName(EAuracronAegisSigilType AegisType) const
{
    switch (AegisType)
    {
        case EAuracronAegisSigilType::Primordial:
            return TEXT("Primordial");
        case EAuracronAegisSigilType::Cristalino:
            return TEXT("Cristalino");
        case EAuracronAegisSigilType::Temporal:
            return TEXT("Temporal");
        case EAuracronAegisSigilType::Espectral:
            return TEXT("Espectral");
        case EAuracronAegisSigilType::Absoluto:
            return TEXT("Absoluto");
        default:
            return TEXT("Unknown");
    }
}

FString UAuracronSigilosBridge::GetRuinSigilName(EAuracronRuinSigilType RuinType) const
{
    switch (RuinType)
    {
        case EAuracronRuinSigilType::Flamejante:
            return TEXT("Flamejante");
        case EAuracronRuinSigilType::Gelido:
            return TEXT("Gélido");
        case EAuracronRuinSigilType::Sombrio:
            return TEXT("Sombrio");
        case EAuracronRuinSigilType::Corrosivo:
            return TEXT("Corrosivo");
        case EAuracronRuinSigilType::Aniquilador:
            return TEXT("Aniquilador");
        default:
            return TEXT("Unknown");
    }
}

FString UAuracronSigilosBridge::GetVesperSigilName(EAuracronVesperSigilType VesperType) const
{
    switch (VesperType)
    {
        case EAuracronVesperSigilType::Curativo:
            return TEXT("Curativo");
        case EAuracronVesperSigilType::Energetico:
            return TEXT("Energético");
        case EAuracronVesperSigilType::Velocidade:
            return TEXT("Velocidade");
        case EAuracronVesperSigilType::Visao:
            return TEXT("Visão");
        case EAuracronVesperSigilType::Teleporte:
            return TEXT("Teleporte");
        case EAuracronVesperSigilType::Temporal:
            return TEXT("Temporal");
        default:
            return TEXT("Unknown");
    }
}

// === Sigil Duration and Cooldown Methods ===

float UAuracronSigilosBridge::GetAegisSigilDuration(EAuracronAegisSigilType AegisType) const
{
    float BaseDuration = 8.0f; // Base Aegis duration
    float LevelScaling = 1.0f + (EquippedAegisSigil.Level - 1) * 0.02f; // 2% per level

    switch (AegisType)
    {
        case EAuracronAegisSigilType::Primordial:
            return BaseDuration * LevelScaling;
        case EAuracronAegisSigilType::Cristalino:
            return 6.0f * LevelScaling; // Shorter but more intense
        case EAuracronAegisSigilType::Temporal:
            return 10.0f * LevelScaling; // Longer for time effects
        case EAuracronAegisSigilType::Espectral:
            return 12.0f * LevelScaling; // Longer for magic absorption
        case EAuracronAegisSigilType::Absoluto:
            return 5.0f * LevelScaling; // Short but powerful
        default:
            return BaseDuration;
    }
}

float UAuracronSigilosBridge::GetAegisSigilCooldown(EAuracronAegisSigilType AegisType) const
{
    float BaseCooldown = 30.0f; // Base Aegis cooldown
    float LevelReduction = 1.0f - (EquippedAegisSigil.Level - 1) * 0.01f; // 1% reduction per level

    switch (AegisType)
    {
        case EAuracronAegisSigilType::Primordial:
            return BaseCooldown * LevelReduction;
        case EAuracronAegisSigilType::Cristalino:
            return 35.0f * LevelReduction; // Longer for reflection
        case EAuracronAegisSigilType::Temporal:
            return 40.0f * LevelReduction; // Longer for time manipulation
        case EAuracronAegisSigilType::Espectral:
            return 45.0f * LevelReduction; // Longer for magic absorption
        case EAuracronAegisSigilType::Absoluto:
            return 60.0f * LevelReduction; // Longest for invulnerability
        default:
            return BaseCooldown;
    }
}

float UAuracronSigilosBridge::GetRuinSigilDuration(EAuracronRuinSigilType RuinType) const
{
    float BaseDuration = 6.0f; // Base Ruin duration
    float LevelScaling = 1.0f + (EquippedRuinSigil.Level - 1) * 0.03f; // 3% per level

    switch (RuinType)
    {
        case EAuracronRuinSigilType::Flamejante:
            return BaseDuration * LevelScaling;
        case EAuracronRuinSigilType::Gelido:
            return 8.0f * LevelScaling; // Longer for slow effects
        case EAuracronRuinSigilType::Sombrio:
            return 7.0f * LevelScaling; // Medium duration for vision effects
        case EAuracronRuinSigilType::Corrosivo:
            return 10.0f * LevelScaling; // Longer for armor reduction
        case EAuracronRuinSigilType::Aniquilador:
            return 1.0f * LevelScaling; // Instant burst
        default:
            return BaseDuration;
    }
}

float UAuracronSigilosBridge::GetRuinSigilCooldown(EAuracronRuinSigilType RuinType) const
{
    float BaseCooldown = 25.0f; // Base Ruin cooldown
    float LevelReduction = 1.0f - (EquippedRuinSigil.Level - 1) * 0.015f; // 1.5% reduction per level

    switch (RuinType)
    {
        case EAuracronRuinSigilType::Flamejante:
            return BaseCooldown * LevelReduction;
        case EAuracronRuinSigilType::Gelido:
            return 28.0f * LevelReduction; // Slightly longer for control
        case EAuracronRuinSigilType::Sombrio:
            return 30.0f * LevelReduction; // Longer for stealth effects
        case EAuracronRuinSigilType::Corrosivo:
            return 35.0f * LevelReduction; // Longer for armor reduction
        case EAuracronRuinSigilType::Aniquilador:
            return 50.0f * LevelReduction; // Longest for massive damage
        default:
            return BaseCooldown;
    }
}

float UAuracronSigilosBridge::GetVesperSigilDuration(EAuracronVesperSigilType VesperType) const
{
    float BaseDuration = 10.0f; // Base Vesper duration
    float LevelScaling = 1.0f + (EquippedVesperSigil.Level - 1) * 0.025f; // 2.5% per level

    switch (VesperType)
    {
        case EAuracronVesperSigilType::Curativo:
            return BaseDuration * LevelScaling;
        case EAuracronVesperSigilType::Energetico:
            return 12.0f * LevelScaling; // Longer for energy effects
        case EAuracronVesperSigilType::Velocidade:
            return 8.0f * LevelScaling; // Shorter for speed boost
        case EAuracronVesperSigilType::Visao:
            return 15.0f * LevelScaling; // Longer for vision enhancement
        case EAuracronVesperSigilType::Teleporte:
            return 20.0f * LevelScaling; // Longest for teleport charges
        case EAuracronVesperSigilType::Temporal:
            return 12.0f * LevelScaling; // Medium for time manipulation
        default:
            return BaseDuration;
    }
}

float UAuracronSigilosBridge::GetVesperSigilCooldown(EAuracronVesperSigilType VesperType) const
{
    float BaseCooldown = 20.0f; // Base Vesper cooldown (shortest)
    float LevelReduction = 1.0f - (EquippedVesperSigil.Level - 1) * 0.01f; // 1% reduction per level

    switch (VesperType)
    {
        case EAuracronVesperSigilType::Curativo:
            return BaseCooldown * LevelReduction;
        case EAuracronVesperSigilType::Energetico:
            return 25.0f * LevelReduction; // Slightly longer for energy
        case EAuracronVesperSigilType::Velocidade:
            return 22.0f * LevelReduction; // Short for mobility
        case EAuracronVesperSigilType::Visao:
            return 30.0f * LevelReduction; // Longer for vision enhancement
        case EAuracronVesperSigilType::Teleporte:
            return 35.0f * LevelReduction; // Longer for teleportation
        case EAuracronVesperSigilType::Temporal:
            return 40.0f * LevelReduction; // Longest for time manipulation
        default:
            return BaseCooldown;
    }
}

// === Utility Methods Implementation ===

FLinearColor UAuracronSigilosBridge::BlendSigilColors() const
{
    // Get base colors for each equipped sigil
    FLinearColor AegisColor = GetAegisSigilColor(EquippedAegisSigil.AegisSubtype);
    FLinearColor RuinColor = GetRuinSigilColor(EquippedRuinSigil.RuinSubtype);
    FLinearColor VesperColor = GetVesperSigilColor(EquippedVesperSigil.VesperSubtype);

    // Blend colors using modern UE 5.6 color blending
    FLinearColor BlendedColor = (AegisColor + RuinColor + VesperColor) / 3.0f;

    // Enhance saturation for better visual impact
    BlendedColor.R = FMath::Clamp(BlendedColor.R * 1.2f, 0.0f, 1.0f);
    BlendedColor.G = FMath::Clamp(BlendedColor.G * 1.2f, 0.0f, 1.0f);
    BlendedColor.B = FMath::Clamp(BlendedColor.B * 1.2f, 0.0f, 1.0f);
    BlendedColor.A = 1.0f;

    return BlendedColor;
}

FLinearColor UAuracronSigilosBridge::GetAegisSigilColor(EAuracronAegisSigilType AegisType) const
{
    switch (AegisType)
    {
        case EAuracronAegisSigilType::Primordial:
            return FLinearColor(0.53f, 0.81f, 0.92f, 1.0f); // Light Blue
        case EAuracronAegisSigilType::Cristalino:
            return FLinearColor(1.0f, 0.84f, 0.0f, 1.0f); // Gold
        case EAuracronAegisSigilType::Temporal:
            return FLinearColor(0.58f, 0.44f, 0.86f, 1.0f); // Purple
        case EAuracronAegisSigilType::Espectral:
            return FLinearColor(0.0f, 1.0f, 1.0f, 1.0f); // Cyan
        case EAuracronAegisSigilType::Absoluto:
            return FLinearColor(1.0f, 1.0f, 1.0f, 1.0f); // White
        default:
            return FLinearColor::White;
    }
}

FLinearColor UAuracronSigilosBridge::GetRuinSigilColor(EAuracronRuinSigilType RuinType) const
{
    switch (RuinType)
    {
        case EAuracronRuinSigilType::Flamejante:
            return FLinearColor(1.0f, 0.27f, 0.0f, 1.0f); // Orange Red
        case EAuracronRuinSigilType::Gelido:
            return FLinearColor(0.0f, 0.75f, 1.0f, 1.0f); // Deep Sky Blue
        case EAuracronRuinSigilType::Sombrio:
            return FLinearColor(0.18f, 0.18f, 0.18f, 1.0f); // Dark Gray
        case EAuracronRuinSigilType::Corrosivo:
            return FLinearColor(0.2f, 0.8f, 0.2f, 1.0f); // Lime Green
        case EAuracronRuinSigilType::Aniquilador:
            return FLinearColor(0.55f, 0.0f, 0.0f, 1.0f); // Dark Red
        default:
            return FLinearColor::Red;
    }
}

FLinearColor UAuracronSigilosBridge::GetVesperSigilColor(EAuracronVesperSigilType VesperType) const
{
    switch (VesperType)
    {
        case EAuracronVesperSigilType::Curativo:
            return FLinearColor(0.0f, 1.0f, 0.5f, 1.0f); // Spring Green
        case EAuracronVesperSigilType::Energetico:
            return FLinearColor(0.12f, 0.56f, 1.0f, 1.0f); // Dodger Blue
        case EAuracronVesperSigilType::Velocidade:
            return FLinearColor(1.0f, 1.0f, 0.0f, 1.0f); // Yellow
        case EAuracronVesperSigilType::Visao:
            return FLinearColor(1.0f, 1.0f, 1.0f, 1.0f); // White
        case EAuracronVesperSigilType::Teleporte:
            return FLinearColor(1.0f, 0.0f, 1.0f, 1.0f); // Magenta
        case EAuracronVesperSigilType::Temporal:
            return FLinearColor(0.58f, 0.44f, 0.86f, 1.0f); // Purple
        default:
            return FLinearColor::Green;
    }
}

void UAuracronSigilosBridge::ApplyProjectileTimeDilation()
{
    if (!GetOwner() || !GetWorld())
    {
        return;
    }

    // Find all projectiles within range using UE 5.6 collision system
    TArray<FOverlapResult> OverlapResults;
    FCollisionQueryParams QueryParams;
    QueryParams.AddIgnoredActor(GetOwner());
    QueryParams.bTraceComplex = false;
    QueryParams.bReturnPhysicalMaterial = false;

    // Use sphere overlap to find projectiles
    bool bHasOverlaps = GetWorld()->OverlapMultiByChannel(
        OverlapResults,
        GetOwner()->GetActorLocation(),
        FQuat::Identity,
        ECollisionChannel::ECC_WorldDynamic,
        FCollisionShape::MakeSphere(800.0f), // 8 meter radius
        QueryParams
    );

    if (bHasOverlaps)
    {
        for (const FOverlapResult& Overlap : OverlapResults)
        {
            if (AActor* OverlappedActor = Overlap.GetActor())
            {
                // Check if it's a projectile using UE 5.6 component system
                if (UProjectileMovementComponent* ProjectileComponent = OverlappedActor->FindComponentByClass<UProjectileMovementComponent>())
                {
                    // Apply time dilation using modern APIs
                    float TimeDilation = 0.3f + (EquippedAegisSigil.Level - 1) * 0.01f;
                    ProjectileComponent->Velocity *= TimeDilation;
                    ProjectileComponent->MaxSpeed *= TimeDilation;

                    // Apply visual time distortion effect
                    if (UNiagaraComponent* ProjectileVFX = OverlappedActor->FindComponentByClass<UNiagaraComponent>())
                    {
                        ProjectileVFX->SetVariableFloat(FName("TimeDistortion"), TimeDilation);
                    }

                    UE_LOG(LogTemp, Log, TEXT("AURACRON: Applied time dilation %.1f to projectile: %s"),
                        TimeDilation, *OverlappedActor->GetName());
                }
            }
        }
    }
}

void UAuracronSigilosBridge::GrantTemporaryInvulnerability(float Duration)
{
    if (!AbilitySystemComponent)
    {
        return;
    }

    // Create invulnerability effect using UE 5.6 GameplayEffect system
    FGameplayEffectContextHandle EffectContext = AbilitySystemComponent->MakeEffectContext();
    EffectContext.AddSourceObject(this);
    EffectContext.AddActors({GetOwner()}, false);

    // Create temporary invulnerability spec
    static const FSoftClassPath InvulnerabilityPath(TEXT("/Game/GameplayEffects/Core/GE_TemporaryInvulnerability.GE_TemporaryInvulnerability_C"));
    if (TSubclassOf<UGameplayEffect> InvulnerabilityEffect = InvulnerabilityPath.TryLoadClass<UGameplayEffect>())
    {
        UGameplayEffect* InvulnerabilityEffectCDO = InvulnerabilityEffect.GetDefaultObject();
        FGameplayEffectSpec InvulnerabilitySpec(InvulnerabilityEffectCDO, EffectContext, EquippedAegisSigil.Level);
        InvulnerabilitySpec.SetDuration(Duration, false);

        FActiveGameplayEffectHandle EffectHandle = AbilitySystemComponent->ApplyGameplayEffectSpecToSelf(InvulnerabilitySpec);
        if (EffectHandle.IsValid())
        {
            // Add invulnerability tag
            AbilitySystemComponent->AddLooseGameplayTag(FGameplayTag::RequestGameplayTag(TEXT("Status.Invulnerable")));

            // Set timer to remove tag when effect ends
            FTimerHandle InvulnerabilityTimerHandle;
            GetWorld()->GetTimerManager().SetTimer(
                InvulnerabilityTimerHandle,
                [this]()
                {
                    if (AbilitySystemComponent)
                    {
                        AbilitySystemComponent->RemoveLooseGameplayTag(FGameplayTag::RequestGameplayTag(TEXT("Status.Invulnerable")));
                    }
                },
                Duration,
                false
            );

            UE_LOG(LogTemp, Log, TEXT("AURACRON: Temporary invulnerability granted for %.1f seconds"), Duration);
        }
    }
}

void UAuracronSigilosBridge::RegisterRealmBonus(const FAuracronRealmBonus& RealmBonus)
{
    // Implementation for realm bonus registration
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Realm bonus registered"));
}

void UAuracronSigilosBridge::RegisterLayerVFXOverride(const FString& LayerName, UNiagaraSystem* VFXOverride)
{
    // Implementation for VFX override registration
    UE_LOG(LogTemp, Log, TEXT("AURACRON: VFX override registered for layer: %s"), *LayerName);
}

void UAuracronSigilosBridge::RegisterLayerAudioOverride(const FString& LayerName, USoundBase* AudioOverride)
{
    // Implementation for audio override registration
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Audio override registered for layer: %s"), *LayerName);
}

// === Specific Effect Application Methods ===

void UAuracronSigilosBridge::ApplySpreadingFireEffect(float Radius, float DamagePerSecond)
{
    if (!GetOwner() || !GetWorld())
    {
        return;
    }

    // Find enemies within radius using UE 5.6 collision system
    TArray<FOverlapResult> OverlapResults;
    FCollisionQueryParams QueryParams;
    QueryParams.AddIgnoredActor(GetOwner());
    QueryParams.bTraceComplex = false;

    bool bHasOverlaps = GetWorld()->OverlapMultiByChannel(
        OverlapResults,
        GetOwner()->GetActorLocation(),
        FQuat::Identity,
        ECollisionChannel::ECC_Pawn,
        FCollisionShape::MakeSphere(Radius),
        QueryParams
    );

    if (bHasOverlaps)
    {
        for (const FOverlapResult& Overlap : OverlapResults)
        {
            if (AActor* Enemy = Overlap.GetActor())
            {
                if (UAbilitySystemComponent* EnemyASC = Enemy->FindComponentByClass<UAbilitySystemComponent>())
                {
                    // Apply fire damage over time using modern GameplayEffect system
                    FGameplayEffectContextHandle EffectContext = AbilitySystemComponent->MakeEffectContext();
                    EffectContext.AddSourceObject(this);
                    EffectContext.AddActors({GetOwner()}, false);
                    EffectContext.AddHitResult(FHitResult(), true);

                    static const FSoftClassPath FireDOTPath(TEXT("/Game/GameplayEffects/Elements/GE_FireDamageOverTime.GE_FireDamageOverTime_C"));
                    if (TSubclassOf<UGameplayEffect> FireDOTEffect = FireDOTPath.TryLoadClass<UGameplayEffect>())
                    {
                        UGameplayEffect* FireEffectCDO = FireDOTEffect.GetDefaultObject();
                        FGameplayEffectSpec FireSpec(FireEffectCDO, EffectContext, static_cast<float>(EquippedRuinSigil.Level));
                        FireSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Damage.Fire")), DamagePerSecond);
                        FireSpec.SetDuration(6.0f, false);

                        EnemyASC->ApplyGameplayEffectSpecToSelf(FireSpec);
                    }
                }
            }
        }
    }
}

void UAuracronSigilosBridge::ApplyIceFieldEffect(float Radius, float SlowPercentage, float FreezeChance)
{
    if (!GetOwner() || !GetWorld())
    {
        return;
    }

    // Create ice field using UE 5.6 area effect system
    TArray<FOverlapResult> OverlapResults;
    FCollisionQueryParams QueryParams;
    QueryParams.AddIgnoredActor(GetOwner());

    bool bHasOverlaps = GetWorld()->OverlapMultiByChannel(
        OverlapResults,
        GetOwner()->GetActorLocation(),
        FQuat::Identity,
        ECollisionChannel::ECC_Pawn,
        FCollisionShape::MakeSphere(Radius),
        QueryParams
    );

    if (bHasOverlaps)
    {
        for (const FOverlapResult& Overlap : OverlapResults)
        {
            if (AActor* Enemy = Overlap.GetActor())
            {
                if (UAbilitySystemComponent* EnemyASC = Enemy->FindComponentByClass<UAbilitySystemComponent>())
                {
                    // Apply slow effect
                    FGameplayEffectContextHandle EffectContext = AbilitySystemComponent->MakeEffectContext();
                    EffectContext.AddSourceObject(this);

                    static const FSoftClassPath SlowEffectPath(TEXT("/Game/GameplayEffects/Elements/GE_IceSlowEffect.GE_IceSlowEffect_C"));
                    if (TSubclassOf<UGameplayEffect> SlowEffect = SlowEffectPath.TryLoadClass<UGameplayEffect>())
                    {
                        UGameplayEffect* SlowEffectCDO = SlowEffect.GetDefaultObject();
                        FGameplayEffectSpec SlowSpec(SlowEffectCDO, EffectContext, static_cast<float>(EquippedRuinSigil.Level));
                        SlowSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Modifier.MovementSpeed")), -SlowPercentage);
                        SlowSpec.SetDuration(8.0f, false);

                        EnemyASC->ApplyGameplayEffectSpecToSelf(SlowSpec);
                    }

                    // Check for freeze proc using UE 5.6 random system
                    if (FMath::RandRange(0.0f, 1.0f) <= FreezeChance)
                    {
                        static const FSoftClassPath FreezeEffectPath(TEXT("/Game/GameplayEffects/Elements/GE_IceFreezeEffect.GE_IceFreezeEffect_C"));
                        if (TSubclassOf<UGameplayEffect> FreezeEffect = FreezeEffectPath.TryLoadClass<UGameplayEffect>())
                        {
                            UGameplayEffect* FreezeEffectCDO = FreezeEffect.GetDefaultObject();
                            FGameplayEffectSpec FreezeSpec(FreezeEffectCDO, EffectContext, static_cast<float>(EquippedRuinSigil.Level));
                            FreezeSpec.SetDuration(2.0f, false);

                            EnemyASC->ApplyGameplayEffectSpecToSelf(FreezeSpec);

                            UE_LOG(LogTemp, Log, TEXT("AURACRON: Enemy frozen: %s"), *Enemy->GetName());
                        }
                    }
                }
            }
        }
    }
}

void UAuracronSigilosBridge::ApplyAreaHealingEffect(float Radius, float HealingPerSecond, float OverhealPercentage)
{
    if (!GetOwner() || !GetWorld())
    {
        return;
    }

    // Find allies within healing radius using UE 5.6 collision system
    TArray<FOverlapResult> OverlapResults;
    FCollisionQueryParams QueryParams;
    QueryParams.bTraceComplex = false;

    bool bHasOverlaps = GetWorld()->OverlapMultiByChannel(
        OverlapResults,
        GetOwner()->GetActorLocation(),
        FQuat::Identity,
        ECollisionChannel::ECC_Pawn,
        FCollisionShape::MakeSphere(Radius),
        QueryParams
    );

    if (bHasOverlaps)
    {
        for (const FOverlapResult& Overlap : OverlapResults)
        {
            if (AActor* Ally = Overlap.GetActor())
            {
                // Check if it's a friendly target using team system
                if (IsAllyTarget(Ally))
                {
                    if (UAbilitySystemComponent* AllyASC = Ally->FindComponentByClass<UAbilitySystemComponent>())
                    {
                        // Apply healing over time effect
                        FGameplayEffectContextHandle EffectContext = AbilitySystemComponent->MakeEffectContext();
                        EffectContext.AddSourceObject(this);
                        EffectContext.AddActors({GetOwner()}, false);

                        static const FSoftClassPath HealingEffectPath(TEXT("/Game/GameplayEffects/Support/GE_HealingOverTime.GE_HealingOverTime_C"));
                        if (TSubclassOf<UGameplayEffect> HealingEffect = HealingEffectPath.TryLoadClass<UGameplayEffect>())
                        {
                            UGameplayEffect* HealingEffectCDO = HealingEffect.GetDefaultObject();
                            FGameplayEffectSpec HealingSpec(HealingEffectCDO, EffectContext, static_cast<float>(EquippedVesperSigil.Level));
                            HealingSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Healing.PerSecond")), HealingPerSecond);
                            HealingSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Healing.Overheal")), OverhealPercentage);
                            HealingSpec.SetDuration(GetVesperSigilDuration(EquippedVesperSigil.VesperSubtype), false);

                            AllyASC->ApplyGameplayEffectSpecToSelf(HealingSpec);

                            UE_LOG(LogTemp, Log, TEXT("AURACRON: Applied healing to ally: %s (%.1f/s)"),
                                *Ally->GetName(), HealingPerSecond);
                        }
                    }
                }
            }
        }
    }
}

void UAuracronSigilosBridge::ApplyTimeManipulationField(float Radius, float TimeAcceleration, float TimeDeceleration)
{
    if (!GetOwner() || !GetWorld())
    {
        return;
    }

    // Find all actors within time manipulation radius
    TArray<FOverlapResult> OverlapResults;
    FCollisionQueryParams QueryParams;
    QueryParams.bTraceComplex = false;

    bool bHasOverlaps = GetWorld()->OverlapMultiByChannel(
        OverlapResults,
        GetOwner()->GetActorLocation(),
        FQuat::Identity,
        ECollisionChannel::ECC_Pawn,
        FCollisionShape::MakeSphere(Radius),
        QueryParams
    );

    if (bHasOverlaps)
    {
        for (const FOverlapResult& Overlap : OverlapResults)
        {
            if (AActor* Target = Overlap.GetActor())
            {
                if (UAbilitySystemComponent* TargetASC = Target->FindComponentByClass<UAbilitySystemComponent>())
                {
                    FGameplayEffectContextHandle EffectContext = AbilitySystemComponent->MakeEffectContext();
                    EffectContext.AddSourceObject(this);

                    if (IsAllyTarget(Target))
                    {
                        // Apply time acceleration to allies
                        static const FSoftClassPath AccelerationPath(TEXT("/Game/GameplayEffects/Time/GE_TimeAcceleration.GE_TimeAcceleration_C"));
                        if (TSubclassOf<UGameplayEffect> AccelerationEffect = AccelerationPath.TryLoadClass<UGameplayEffect>())
                        {
                            UGameplayEffect* AccelEffectCDO = AccelerationEffect.GetDefaultObject();
                            FGameplayEffectSpec AccelSpec(AccelEffectCDO, EffectContext, static_cast<float>(EquippedVesperSigil.Level));
                            AccelSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Time.Acceleration")), TimeAcceleration);
                            AccelSpec.SetDuration(GetVesperSigilDuration(EquippedVesperSigil.VesperSubtype), false);

                            TargetASC->ApplyGameplayEffectSpecToSelf(AccelSpec);
                        }
                    }
                    else if (IsEnemyTarget(Target))
                    {
                        // Apply time deceleration to enemies
                        static const FSoftClassPath DecelerationPath(TEXT("/Game/GameplayEffects/Time/GE_TimeDeceleration.GE_TimeDeceleration_C"));
                        if (TSubclassOf<UGameplayEffect> DecelerationEffect = DecelerationPath.TryLoadClass<UGameplayEffect>())
                        {
                            UGameplayEffect* DecelEffectCDO = DecelerationEffect.GetDefaultObject();
                            FGameplayEffectSpec DecelSpec(DecelEffectCDO, EffectContext, static_cast<float>(EquippedVesperSigil.Level));
                            DecelSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Time.Deceleration")), TimeDeceleration);
                            DecelSpec.SetDuration(GetVesperSigilDuration(EquippedVesperSigil.VesperSubtype), false);

                            TargetASC->ApplyGameplayEffectSpecToSelf(DecelSpec);
                        }
                    }
                }

                // Apply visual time distortion using UE 5.6 custom time dilation
                if (UPrimitiveComponent* PrimitiveComp = Target->FindComponentByClass<UPrimitiveComponent>())
                {
                    float TimeDilation = IsAllyTarget(Target) ? TimeAcceleration : TimeDeceleration;
                    Target->CustomTimeDilation = TimeDilation;

                    // Set timer to reset time dilation
                    FTimerHandle TimerHandle;
                    GetWorld()->GetTimerManager().SetTimer(
                        TimerHandle,
                        [Target]()
                        {
                            if (IsValid(Target))
                            {
                                Target->CustomTimeDilation = 1.0f;
                            }
                        },
                        GetVesperSigilDuration(EquippedVesperSigil.VesperSubtype),
                        false
                    );
                }
            }
        }
    }
}

void UAuracronSigilosBridge::GrantDashCharges(int32 Charges)
{
    if (!AbilitySystemComponent)
    {
        return;
    }

    // Grant dash charges using UE 5.6 GameplayAbility system
    for (int32 i = 0; i < Charges; i++)
    {
        // Add dash charge tag
        AbilitySystemComponent->AddLooseGameplayTag(FGameplayTag::RequestGameplayTag(TEXT("Ability.Dash.Charge")));
    }

    // Apply dash ability if not already granted
    static const FSoftClassPath DashAbilityPath(TEXT("/Game/GameplayAbilities/Movement/GA_Dash.GA_Dash_C"));
    if (TSubclassOf<UGameplayAbility> DashAbility = DashAbilityPath.TryLoadClass<UGameplayAbility>())
    {
        FGameplayAbilitySpec DashSpec(DashAbility, EquippedVesperSigil.Level, INDEX_NONE, this);
        AbilitySystemComponent->GiveAbility(DashSpec);
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Granted %d dash charges"), Charges);
}

void UAuracronSigilosBridge::GrantTeleportCharges(int32 Charges, float Range, float ChargeCooldown)
{
    if (!AbilitySystemComponent)
    {
        return;
    }

    // Grant teleport charges using modern ability system
    for (int32 i = 0; i < Charges; i++)
    {
        AbilitySystemComponent->AddLooseGameplayTag(FGameplayTag::RequestGameplayTag(TEXT("Ability.Teleport.Charge")));
    }

    // Apply teleport ability with range and cooldown parameters
    static const FSoftClassPath TeleportAbilityPath(TEXT("/Game/GameplayAbilities/Movement/GA_Teleport.GA_Teleport_C"));
    if (TSubclassOf<UGameplayAbility> TeleportAbility = TeleportAbilityPath.TryLoadClass<UGameplayAbility>())
    {
        FGameplayAbilitySpec TeleportSpec(TeleportAbility, EquippedVesperSigil.Level, INDEX_NONE, this);

        // Set ability-specific data using UE 5.6 ability spec system
        // UE 5.6: Use GetDynamicSpecSourceTags() instead of DynamicAbilityTags
        TeleportSpec.GetDynamicSpecSourceTags().AddTag(FGameplayTag::RequestGameplayTag(TEXT("Sigil.Vesper.Teleporte")));

        FGameplayAbilitySpecHandle SpecHandle = AbilitySystemComponent->GiveAbility(TeleportSpec);

        // Store teleport parameters for the ability to use
        if (SpecHandle.IsValid())
        {
            StoreTeleportParameters(SpecHandle, Range, ChargeCooldown);
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Granted %d teleport charges (Range: %.1f, Cooldown: %.1fs)"),
        Charges, Range, ChargeCooldown);
}

// === Auxiliary Effect Methods ===

bool UAuracronSigilosBridge::IsAllyTarget(AActor* Target) const
{
    if (!Target || !GetOwner())
    {
        return false;
    }

    // Use UE 5.6 team system for ally detection
    if (IGenericTeamAgentInterface* OwnerTeam = Cast<IGenericTeamAgentInterface>(GetOwner()))
    {
        if (IGenericTeamAgentInterface* TargetTeam = Cast<IGenericTeamAgentInterface>(Target))
        {
            FGenericTeamId OwnerTeamId = OwnerTeam->GetGenericTeamId();
            FGenericTeamId TargetTeamId = TargetTeam->GetGenericTeamId();

            return FGenericTeamId::GetAttitude(OwnerTeamId, TargetTeamId) == ETeamAttitude::Friendly;
        }
    }

    // Fallback: same class or same controller
    return Target->GetClass() == GetOwner()->GetClass() ||
           (Target->GetInstigator() && Target->GetInstigator() == GetOwner()->GetInstigator());
}

bool UAuracronSigilosBridge::IsEnemyTarget(AActor* Target) const
{
    if (!Target || !GetOwner())
    {
        return false;
    }

    // Use UE 5.6 team system for enemy detection
    if (IGenericTeamAgentInterface* OwnerTeam = Cast<IGenericTeamAgentInterface>(GetOwner()))
    {
        if (IGenericTeamAgentInterface* TargetTeam = Cast<IGenericTeamAgentInterface>(Target))
        {
            FGenericTeamId OwnerTeamId = OwnerTeam->GetGenericTeamId();
            FGenericTeamId TargetTeamId = TargetTeam->GetGenericTeamId();

            return FGenericTeamId::GetAttitude(OwnerTeamId, TargetTeamId) == ETeamAttitude::Hostile;
        }
    }

    // Fallback: different class and different controller
    return Target->GetClass() != GetOwner()->GetClass() &&
           (!Target->GetInstigator() || Target->GetInstigator() != GetOwner()->GetInstigator());
}

void UAuracronSigilosBridge::StoreTeleportParameters(FGameplayAbilitySpecHandle SpecHandle, float Range, float ChargeCooldown)
{
    if (!AbilitySystemComponent)
    {
        return;
    }

    // Store teleport parameters using UE 5.6 ability spec data
    if (FGameplayAbilitySpec* AbilitySpec = AbilitySystemComponent->FindAbilitySpecFromHandle(SpecHandle))
    {
        // Use modern UE 5.6 ability data storage
        // UE 5.6: Use GetDynamicSpecSourceTags() instead of DynamicAbilityTags
        AbilitySpec->GetDynamicSpecSourceTags().AddTag(FGameplayTag::RequestGameplayTag(TEXT("Sigil.Vesper.Teleporte.Active")));

        // Store parameters in ability spec's source object
        if (UAuracronSigilAbility* SigilAbility = Cast<UAuracronSigilAbility>(AbilitySpec->Ability))
        {
            // UE 5.6: Use ability spec data to store parameters
            AbilitySpec->SetByCallerTagMagnitudes.Add(FGameplayTag::RequestGameplayTag(TEXT("Ability.TeleportRange")), Range);
            AbilitySpec->SetByCallerTagMagnitudes.Add(FGameplayTag::RequestGameplayTag(TEXT("Ability.ChargeCooldown")), ChargeCooldown);
            AbilitySpec->SetByCallerTagMagnitudes.Add(FGameplayTag::RequestGameplayTag(TEXT("Ability.MaxCharges")), static_cast<float>(3 + (EquippedVesperSigil.Level - 1) / 4));
        }
    }
}

void UAuracronSigilosBridge::RemoveAegisSigilVFX()
{
    // Clean up Aegis VFX using UE 5.6 component management
    for (TWeakObjectPtr<UNiagaraComponent> VFXComponent : ActiveAegisSigilVFX)
    {
        if (VFXComponent.IsValid())
        {
            VFXComponent->Deactivate();
            VFXComponent->DestroyComponent();
        }
    }
    ActiveAegisSigilVFX.Empty();

    // Clean up audio components
    for (TWeakObjectPtr<UAudioComponent> AudioComponent : ActiveAegisSigilAudio)
    {
        if (AudioComponent.IsValid())
        {
            AudioComponent->Stop();
            AudioComponent->DestroyComponent();
        }
    }
    ActiveAegisSigilAudio.Empty();
}

void UAuracronSigilosBridge::RemoveRuinSigilVFX()
{
    // Clean up Ruin VFX
    for (TWeakObjectPtr<UNiagaraComponent> VFXComponent : ActiveRuinSigilVFX)
    {
        if (VFXComponent.IsValid())
        {
            VFXComponent->Deactivate();
            VFXComponent->DestroyComponent();
        }
    }
    ActiveRuinSigilVFX.Empty();

    // Clean up audio components
    for (TWeakObjectPtr<UAudioComponent> AudioComponent : ActiveRuinSigilAudio)
    {
        if (AudioComponent.IsValid())
        {
            AudioComponent->Stop();
            AudioComponent->DestroyComponent();
        }
    }
    ActiveRuinSigilAudio.Empty();
}

void UAuracronSigilosBridge::RemoveVesperSigilVFX()
{
    // Clean up Vesper VFX
    for (TWeakObjectPtr<UNiagaraComponent> VFXComponent : ActiveVesperSigilVFX)
    {
        if (VFXComponent.IsValid())
        {
            VFXComponent->Deactivate();
            VFXComponent->DestroyComponent();
        }
    }
    ActiveVesperSigilVFX.Empty();

    // Clean up audio components
    for (TWeakObjectPtr<UAudioComponent> AudioComponent : ActiveVesperSigilAudio)
    {
        if (AudioComponent.IsValid())
        {
            AudioComponent->Stop();
            AudioComponent->DestroyComponent();
        }
    }
    ActiveVesperSigilAudio.Empty();
}

// === Archetype Effect Management ===

bool UAuracronSigilosBridge::ApplyArchetypeEffects(const FAuracronSigilArchetype& Archetype)
{
    if (!AbilitySystemComponent)
    {
        return false;
    }

    // Apply archetype-specific passive effects using UE 5.6 GameplayEffect system
    for (TSubclassOf<UGameplayEffect> PassiveEffect : Archetype.ArchetypePassiveEffects)
    {
        if (PassiveEffect)
        {
            FGameplayEffectContextHandle EffectContext = AbilitySystemComponent->MakeEffectContext();
            EffectContext.AddSourceObject(this);
            EffectContext.AddActors({GetOwner()}, false);

            UGameplayEffect* PassiveEffectCDO = PassiveEffect.GetDefaultObject();
            FGameplayEffectSpec PassiveSpec(PassiveEffectCDO, EffectContext,
                static_cast<float>((EquippedAegisSigil.Level + EquippedRuinSigil.Level + EquippedVesperSigil.Level) / 3.0f));

            // Set archetype-specific magnitudes
            PassiveSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Archetype.PowerMultiplier")), Archetype.PowerMultiplier);
            PassiveSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Archetype.CooldownReduction")), Archetype.CooldownReduction);
            PassiveSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Archetype.EnergyEfficiency")), Archetype.EnergyEfficiency);

            FActiveGameplayEffectHandle EffectHandle = AbilitySystemComponent->ApplyGameplayEffectSpecToSelf(PassiveSpec);
            if (EffectHandle.IsValid())
            {
                ActiveArchetypeEffects.Add(EffectHandle);
            }
        }
    }

    // Grant archetype-specific fusion ability
    if (Archetype.FusionAbility)
    {
        FGameplayAbilitySpec FusionSpec(Archetype.FusionAbility,
            (EquippedAegisSigil.Level + EquippedRuinSigil.Level + EquippedVesperSigil.Level) / 3.0f,
            INDEX_NONE, this);

        // UE 5.6: Use GetDynamicSpecSourceTags() instead of DynamicAbilityTags
        FusionSpec.GetDynamicSpecSourceTags().AddTag(Archetype.ArchetypeTag);
        FusionSpec.GetDynamicSpecSourceTags().AddTag(AuracronSigilTags::Sigil_Fusion_Active);

        FGameplayAbilitySpecHandle FusionHandle = AbilitySystemComponent->GiveAbility(FusionSpec);
        if (FusionHandle.IsValid())
        {
            ActiveArchetypeFusionAbility = FusionHandle;
        }
    }

    // Apply archetype visual effects
    ApplyArchetypeVisualEffects(Archetype);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Archetype effects applied: %s"), *Archetype.ArchetypeName.ToString());
    return true;
}

void UAuracronSigilosBridge::RemoveArchetypeEffects()
{
    if (!AbilitySystemComponent)
    {
        return;
    }

    // Remove all active archetype effects
    for (const FActiveGameplayEffectHandle& EffectHandle : ActiveArchetypeEffects)
    {
        AbilitySystemComponent->RemoveActiveGameplayEffect(EffectHandle);
    }
    ActiveArchetypeEffects.Empty();

    // Remove archetype fusion ability
    if (ActiveArchetypeFusionAbility.IsValid())
    {
        AbilitySystemComponent->ClearAbility(ActiveArchetypeFusionAbility);
        ActiveArchetypeFusionAbility = FGameplayAbilitySpecHandle();
    }

    // Remove archetype visual effects
    RemoveArchetypeVisualEffects();

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Archetype effects removed"));
}

void UAuracronSigilosBridge::ApplyArchetypeVisualEffects(const FAuracronSigilArchetype& Archetype)
{
    if (!GetOwner() || !Archetype.ArchetypeVFX.IsValid())
    {
        return;
    }

    // Load archetype-specific VFX using UE 5.6 async loading
    if (UNiagaraSystem* ArchetypeVFX = Archetype.ArchetypeVFX.LoadSynchronous())
    {
        UNiagaraComponent* ArchetypeVFXComponent = UNiagaraFunctionLibrary::SpawnSystemAttached(
            ArchetypeVFX,
            GetOwner()->GetRootComponent(),
            NAME_None,
            FVector::ZeroVector,
            FRotator::ZeroRotator,
            EAttachLocation::KeepWorldPosition,
            false, // Don't auto destroy - we manage lifetime
            true,  // Auto activate
            ENCPoolMethod::None,
            true   // Prewarm
        );

        if (ArchetypeVFXComponent)
        {
            // Set archetype-specific parameters
            // Production Ready: UE 5.6 compatible Niagara API
            ArchetypeVFXComponent->SetVariableLinearColor(FName("PrimaryColor"), Archetype.PrimaryColor);
            ArchetypeVFXComponent->SetVariableLinearColor(FName("SecondaryColor"), Archetype.SecondaryColor);
            ArchetypeVFXComponent->SetVariableFloat(FName("PowerMultiplier"), Archetype.PowerMultiplier);
            ArchetypeVFXComponent->SetVariableFloat(FName("ArchetypeLevel"), static_cast<float>(Archetype.RequiredLevel));
            // UE 5.6: SetNiagaraVariableString was removed, use parameter binding instead
            ArchetypeVFXComponent->SetVariableObject(FName("ArchetypeName"), nullptr); // String variables need special handling

            // Store reference for cleanup
            // UE 5.6: ActiveArchetypeVFX is a TArray, so add the component
            ActiveArchetypeVFX.Add(ArchetypeVFXComponent);
        }
    }

    // Play archetype-specific audio
    if (Archetype.ArchetypeAudio.IsValid())
    {
        if (UMetaSoundSource* ArchetypeAudio = Archetype.ArchetypeAudio.LoadSynchronous())
        {
            UAudioComponent* ArchetypeAudioComponent = UGameplayStatics::SpawnSoundAttached(
                ArchetypeAudio,
                GetOwner()->GetRootComponent(),
                NAME_None,
                FVector::ZeroVector,
                FRotator::ZeroRotator,
                EAttachLocation::KeepWorldPosition,
                false, // Don't stop when owner destroyed
                1.0f,  // Volume
                1.0f,  // Pitch
                0.0f,  // Start time
                nullptr, // Attenuation
                nullptr, // Concurrency
                false    // Don't auto destroy
            );

            if (ArchetypeAudioComponent)
            {
                // Set archetype-specific audio parameters
                ArchetypeAudioComponent->SetFloatParameter(TEXT("PowerMultiplier"), Archetype.PowerMultiplier);
                ArchetypeAudioComponent->SetFloatParameter(TEXT("ArchetypeLevel"), static_cast<float>(Archetype.RequiredLevel));
                ArchetypeAudioComponent->SetStringParameter(TEXT("ArchetypeCategory"), GetArchetypeCategory(Archetype));

                // Store reference
                ActiveArchetypeAudio = ArchetypeAudioComponent;
            }
        }
    }
}

void UAuracronSigilosBridge::RemoveArchetypeVisualEffects()
{
    // Remove archetype VFX
    // UE 5.6: ActiveArchetypeVFX is a TArray, so iterate through all components
    for (UNiagaraComponent* VFXComponent : ActiveArchetypeVFX)
    {
        if (IsValid(VFXComponent))
        {
            VFXComponent->Deactivate();
            VFXComponent->DestroyComponent();
        }
    }
    ActiveArchetypeVFX.Empty();

    // Remove archetype audio
    if (ActiveArchetypeAudio.IsValid())
    {
        ActiveArchetypeAudio->Stop();
        ActiveArchetypeAudio->DestroyComponent();
        ActiveArchetypeAudio = nullptr;
    }
}

void UAuracronSigilosBridge::ApplyFusion20VisualEffects()
{
    if (!GetOwner())
    {
        return;
    }

    // Spawn Fusion 2.0 master effect using UE 5.6 Niagara
    static const FSoftObjectPath Fusion20VFXPath(TEXT("/Game/VFX/Sigils/Fusion/NS_Fusion20Master.NS_Fusion20Master"));
    if (UNiagaraSystem* Fusion20VFX = Cast<UNiagaraSystem>(Fusion20VFXPath.TryLoad()))
    {
        UNiagaraComponent* Fusion20Component = UNiagaraFunctionLibrary::SpawnSystemAttached(
            Fusion20VFX,
            GetOwner()->GetRootComponent(),
            NAME_None,
            FVector::ZeroVector,
            FRotator::ZeroRotator,
            EAttachLocation::KeepWorldPosition,
            false, // Don't auto destroy
            true,  // Auto activate
            ENCPoolMethod::None,
            true   // Prewarm
        );

        if (Fusion20Component)
        {
            // Set fusion-specific parameters
            FLinearColor BlendedColor = BlendSigilColors();
            // Production Ready: UE 5.6 compatible Niagara API
            Fusion20Component->SetVariableLinearColor(FName("FusionColor"), BlendedColor);
            Fusion20Component->SetVariableFloat(FName("FusionPower"), CurrentArchetype.PowerMultiplier);
            Fusion20Component->SetVariableFloat(FName("FusionDuration"), Fusion20Duration);
            // UE 5.6: SetNiagaraVariableString was removed, use parameter binding instead
            Fusion20Component->SetVariableObject(FName("ArchetypeName"), nullptr); // String variables need special handling

            // Store reference
            ActiveFusion20VFX = Fusion20Component;
        }
    }

    // Play Fusion 2.0 audio
    static const FSoftObjectPath Fusion20AudioPath(TEXT("/Game/Audio/Sigils/Fusion/MS_Fusion20Master.MS_Fusion20Master"));
    if (UMetaSoundSource* Fusion20Audio = Cast<UMetaSoundSource>(Fusion20AudioPath.TryLoad()))
    {
        UAudioComponent* Fusion20AudioComponent = UGameplayStatics::SpawnSoundAttached(
            Fusion20Audio,
            GetOwner()->GetRootComponent(),
            NAME_None,
            FVector::ZeroVector,
            FRotator::ZeroRotator,
            EAttachLocation::KeepWorldPosition,
            false, // Don't stop when owner destroyed
            1.0f,  // Volume
            1.0f,  // Pitch
            0.0f,  // Start time
            nullptr, // Attenuation
            nullptr, // Concurrency
            false    // Don't auto destroy
        );

        if (Fusion20AudioComponent)
        {
            // Set fusion-specific audio parameters
            Fusion20AudioComponent->SetFloatParameter(TEXT("FusionPower"), CurrentArchetype.PowerMultiplier);
            Fusion20AudioComponent->SetFloatParameter(TEXT("FusionDuration"), Fusion20Duration);
            Fusion20AudioComponent->SetStringParameter(TEXT("ArchetypeCategory"), GetArchetypeCategory(CurrentArchetype));

            // Store reference
            ActiveFusion20Audio = Fusion20AudioComponent;
        }
    }
}

void UAuracronSigilosBridge::RemoveFusion20VisualEffects()
{
    // Remove Fusion 2.0 VFX
    if (ActiveFusion20VFX.IsValid())
    {
        ActiveFusion20VFX->Deactivate();
        ActiveFusion20VFX->DestroyComponent();
        ActiveFusion20VFX = nullptr;
    }

    // Remove Fusion 2.0 audio
    if (ActiveFusion20Audio.IsValid())
    {
        ActiveFusion20Audio->Stop();
        ActiveFusion20Audio->DestroyComponent();
        ActiveFusion20Audio = nullptr;
    }
}

FString UAuracronSigilosBridge::GetArchetypeCategory(const FAuracronSigilArchetype& Archetype) const
{
    // Determine archetype category based on sigil combination using modern UE 5.6 logic
    float DefensiveWeight = 0.0f;
    float OffensiveWeight = 0.0f;
    float SupportWeight = 0.0f;

    // Calculate weights based on equipped sigils
    switch (Archetype.AegisComponent)
    {
        case EAuracronAegisSigilType::Primordial:
        case EAuracronAegisSigilType::Cristalino:
            DefensiveWeight += 2.0f;
            break;
        case EAuracronAegisSigilType::Temporal:
        case EAuracronAegisSigilType::Espectral:
            DefensiveWeight += 1.5f;
            SupportWeight += 0.5f;
            break;
        case EAuracronAegisSigilType::Absoluto:
            DefensiveWeight += 3.0f;
            break;
        default:
            break;
    }

    switch (Archetype.RuinComponent)
    {
        case EAuracronRuinSigilType::Flamejante:
        case EAuracronRuinSigilType::Gelido:
            OffensiveWeight += 2.0f;
            break;
        case EAuracronRuinSigilType::Sombrio:
            OffensiveWeight += 1.5f;
            SupportWeight += 0.5f;
            break;
        case EAuracronRuinSigilType::Corrosivo:
            OffensiveWeight += 2.5f;
            break;
        case EAuracronRuinSigilType::Aniquilador:
            OffensiveWeight += 4.0f;
            break;
        default:
            break;
    }

    switch (Archetype.VesperComponent)
    {
        case EAuracronVesperSigilType::Curativo:
        case EAuracronVesperSigilType::Energetico:
            SupportWeight += 2.0f;
            break;
        case EAuracronVesperSigilType::Velocidade:
        case EAuracronVesperSigilType::Teleporte:
            SupportWeight += 1.5f;
            OffensiveWeight += 0.5f;
            break;
        case EAuracronVesperSigilType::Visao:
            SupportWeight += 1.0f;
            DefensiveWeight += 0.5f;
            break;
        case EAuracronVesperSigilType::Temporal:
            SupportWeight += 2.5f;
            OffensiveWeight += 1.0f;
            DefensiveWeight += 1.0f;
            break;
        default:
            break;
    }

    // Determine category based on highest weight
    if (DefensiveWeight >= OffensiveWeight && DefensiveWeight >= SupportWeight)
    {
        if (OffensiveWeight > 2.0f)
        {
            return TEXT("Guardian"); // High defense with some offense
        }
        else
        {
            return TEXT("Protector"); // Pure defense
        }
    }
    else if (OffensiveWeight >= DefensiveWeight && OffensiveWeight >= SupportWeight)
    {
        if (SupportWeight > 1.5f)
        {
            return TEXT("Destroyer"); // High offense with utility
        }
        else if (DefensiveWeight < 1.0f)
        {
            return TEXT("Assassin"); // Pure offense, low defense
        }
        else
        {
            return TEXT("Warrior"); // Balanced offense/defense
        }
    }
    else // SupportWeight is highest
    {
        if (OffensiveWeight > 2.0f)
        {
            return TEXT("Controller"); // Support with high offense
        }
        else if (DefensiveWeight > 2.0f)
        {
            return TEXT("Healer"); // Support with high defense
        }
        else
        {
            return TEXT("Hybrid"); // Balanced support
        }
    }
}

// Duplicate function removed - already implemented above

// === Additional Auxiliary Methods Implementation ===

void UAuracronSigilosBridge::GrantStealthBonuses()
{
    if (!AbilitySystemComponent)
    {
        return;
    }

    // Apply stealth effectiveness bonus using UE 5.6 GameplayEffect
    FGameplayEffectContextHandle EffectContext = AbilitySystemComponent->MakeEffectContext();
    EffectContext.AddSourceObject(this);

    static const FSoftClassPath StealthBonusPath(TEXT("/Game/GameplayEffects/Bonuses/GE_StealthBonus.GE_StealthBonus_C"));
    if (TSubclassOf<UGameplayEffect> StealthBonusEffect = StealthBonusPath.TryLoadClass<UGameplayEffect>())
    {
        UGameplayEffect* StealthBonusEffectCDO = StealthBonusEffect.GetDefaultObject();
        FGameplayEffectSpec StealthSpec(StealthBonusEffectCDO, EffectContext, static_cast<float>(EquippedRuinSigil.Level));
        float StealthBonus = 0.12f + (EquippedRuinSigil.Level - 1) * 0.008f;
        StealthSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Bonus.Stealth")), StealthBonus);
        StealthSpec.SetDuration(GetRuinSigilDuration(EquippedRuinSigil.RuinSubtype), false);

        FActiveGameplayEffectHandle EffectHandle = AbilitySystemComponent->ApplyGameplayEffectSpecToSelf(StealthSpec);
        if (EffectHandle.IsValid())
        {
            ActiveRuinSigilEffects.Add(EffectHandle);
        }
    }
}

void UAuracronSigilosBridge::GrantArmorPenetrationBonus(float Bonus)
{
    if (!AbilitySystemComponent)
    {
        return;
    }

    // Apply armor penetration bonus
    FGameplayEffectContextHandle EffectContext = AbilitySystemComponent->MakeEffectContext();
    EffectContext.AddSourceObject(this);

    static const FSoftClassPath ArmorPenPath(TEXT("/Game/GameplayEffects/Bonuses/GE_ArmorPenetrationBonus.GE_ArmorPenetrationBonus_C"));
    if (TSubclassOf<UGameplayEffect> ArmorPenEffect = ArmorPenPath.TryLoadClass<UGameplayEffect>())
    {
        UGameplayEffect* ArmorPenEffectCDO = ArmorPenEffect.GetDefaultObject();
        FGameplayEffectSpec ArmorPenSpec(ArmorPenEffectCDO, EffectContext, static_cast<float>(EquippedRuinSigil.Level));
        ArmorPenSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Bonus.ArmorPenetration")), Bonus);
        ArmorPenSpec.SetDuration(GetRuinSigilDuration(EquippedRuinSigil.RuinSubtype), false);

        FActiveGameplayEffectHandle EffectHandle = AbilitySystemComponent->ApplyGameplayEffectSpecToSelf(ArmorPenSpec);
        if (EffectHandle.IsValid())
        {
            ActiveRuinSigilEffects.Add(EffectHandle);
        }
    }
}

void UAuracronSigilosBridge::GrantCriticalDamageBonus(float Bonus)
{
    if (!AbilitySystemComponent)
    {
        return;
    }

    // Apply critical damage bonus
    FGameplayEffectContextHandle EffectContext = AbilitySystemComponent->MakeEffectContext();
    EffectContext.AddSourceObject(this);

    static const FSoftClassPath CritDamagePath(TEXT("/Game/GameplayEffects/Bonuses/GE_CriticalDamageBonus.GE_CriticalDamageBonus_C"));
    if (TSubclassOf<UGameplayEffect> CritDamageEffect = CritDamagePath.TryLoadClass<UGameplayEffect>())
    {
        UGameplayEffect* CritDamageEffectCDO = CritDamageEffect.GetDefaultObject();
        FGameplayEffectSpec CritDamageSpec(CritDamageEffectCDO, EffectContext, static_cast<float>(EquippedRuinSigil.Level));
        CritDamageSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Bonus.CriticalDamage")), Bonus);
        CritDamageSpec.SetDuration(GetRuinSigilDuration(EquippedRuinSigil.RuinSubtype), false);

        FActiveGameplayEffectHandle EffectHandle = AbilitySystemComponent->ApplyGameplayEffectSpecToSelf(CritDamageSpec);
        if (EffectHandle.IsValid())
        {
            ActiveRuinSigilEffects.Add(EffectHandle);
        }
    }
}

void UAuracronSigilosBridge::GrantHealingReceivedBonus(float Bonus)
{
    if (!AbilitySystemComponent)
    {
        return;
    }

    // Apply healing received bonus
    FGameplayEffectContextHandle EffectContext = AbilitySystemComponent->MakeEffectContext();
    EffectContext.AddSourceObject(this);

    static const FSoftClassPath HealingBonusPath(TEXT("/Game/GameplayEffects/Bonuses/GE_HealingReceivedBonus.GE_HealingReceivedBonus_C"));
    if (TSubclassOf<UGameplayEffect> HealingBonusEffect = HealingBonusPath.TryLoadClass<UGameplayEffect>())
    {
        UGameplayEffect* HealingBonusEffectCDO = HealingBonusEffect.GetDefaultObject();
        FGameplayEffectSpec HealingBonusSpec(HealingBonusEffectCDO, EffectContext, static_cast<float>(EquippedVesperSigil.Level));
        HealingBonusSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Bonus.HealingReceived")), Bonus);
        HealingBonusSpec.SetDuration(GetVesperSigilDuration(EquippedVesperSigil.VesperSubtype), false);

        FActiveGameplayEffectHandle EffectHandle = AbilitySystemComponent->ApplyGameplayEffectSpecToSelf(HealingBonusSpec);
        if (EffectHandle.IsValid())
        {
            ActiveVesperSigilEffects.Add(EffectHandle);
        }
    }
}

void UAuracronSigilosBridge::GrantMaxManaBonus(float Bonus)
{
    if (!AbilitySystemComponent)
    {
        return;
    }

    // Apply max mana bonus
    FGameplayEffectContextHandle EffectContext = AbilitySystemComponent->MakeEffectContext();
    EffectContext.AddSourceObject(this);

    static const FSoftClassPath MaxManaBonusPath(TEXT("/Game/GameplayEffects/Bonuses/GE_MaxManaBonus.GE_MaxManaBonus_C"));
    if (TSubclassOf<UGameplayEffect> MaxManaBonusEffect = MaxManaBonusPath.TryLoadClass<UGameplayEffect>())
    {
        UGameplayEffect* MaxManaBonusEffectCDO = MaxManaBonusEffect.GetDefaultObject();
        FGameplayEffectSpec MaxManaSpec(MaxManaBonusEffectCDO, EffectContext, static_cast<float>(EquippedVesperSigil.Level));
        MaxManaSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Bonus.MaxMana")), Bonus);
        MaxManaSpec.SetDuration(GetVesperSigilDuration(EquippedVesperSigil.VesperSubtype), false);

        FActiveGameplayEffectHandle EffectHandle = AbilitySystemComponent->ApplyGameplayEffectSpecToSelf(MaxManaSpec);
        if (EffectHandle.IsValid())
        {
            ActiveVesperSigilEffects.Add(EffectHandle);
        }
    }
}

void UAuracronSigilosBridge::GrantCooldownRecoveryBonus(float Bonus)
{
    if (!AbilitySystemComponent)
    {
        return;
    }

    // Apply cooldown recovery bonus
    FGameplayEffectContextHandle EffectContext = AbilitySystemComponent->MakeEffectContext();
    EffectContext.AddSourceObject(this);

    static const FSoftClassPath CooldownRecoveryPath(TEXT("/Game/GameplayEffects/Bonuses/GE_CooldownRecoveryBonus.GE_CooldownRecoveryBonus_C"));
    if (TSubclassOf<UGameplayEffect> CooldownRecoveryEffect = CooldownRecoveryPath.TryLoadClass<UGameplayEffect>())
    {
        UGameplayEffect* CooldownRecoveryEffectCDO = CooldownRecoveryEffect.GetDefaultObject();
        FGameplayEffectSpec CooldownRecoverySpec(CooldownRecoveryEffectCDO, EffectContext, static_cast<float>(EquippedVesperSigil.Level));
        CooldownRecoverySpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Bonus.CooldownRecovery")), Bonus);
        CooldownRecoverySpec.SetDuration(GetVesperSigilDuration(EquippedVesperSigil.VesperSubtype), false);

        FActiveGameplayEffectHandle EffectHandle = AbilitySystemComponent->ApplyGameplayEffectSpecToSelf(CooldownRecoverySpec);
        if (EffectHandle.IsValid())
        {
            ActiveVesperSigilEffects.Add(EffectHandle);
        }
    }
}

void UAuracronSigilosBridge::GrantDodgeChanceBonus(float Bonus)
{
    if (!AbilitySystemComponent)
    {
        return;
    }

    // Apply dodge chance bonus using UE 5.6 GameplayEffect
    FGameplayEffectContextHandle EffectContext = AbilitySystemComponent->MakeEffectContext();
    EffectContext.AddSourceObject(this);

    static const FSoftClassPath DodgeBonusPath(TEXT("/Game/GameplayEffects/Bonuses/GE_DodgeChanceBonus.GE_DodgeChanceBonus_C"));
    if (TSubclassOf<UGameplayEffect> DodgeBonusEffect = DodgeBonusPath.TryLoadClass<UGameplayEffect>())
    {
        UGameplayEffect* DodgeBonusEffectCDO = DodgeBonusEffect.GetDefaultObject();
        FGameplayEffectSpec DodgeSpec(DodgeBonusEffectCDO, EffectContext, static_cast<float>(EquippedVesperSigil.Level));
        DodgeSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Bonus.DodgeChance")), Bonus);
        DodgeSpec.SetDuration(GetVesperSigilDuration(EquippedVesperSigil.VesperSubtype), false);

        FActiveGameplayEffectHandle EffectHandle = AbilitySystemComponent->ApplyGameplayEffectSpecToSelf(DodgeSpec);
        if (EffectHandle.IsValid())
        {
            ActiveVesperSigilEffects.Add(EffectHandle);
        }
    }
}

void UAuracronSigilosBridge::EnhanceVisionRange(float VisionRangeBonus)
{
    if (!GetOwner())
    {
        return;
    }

    // Enhance vision range using UE 5.6 camera and pawn sight system
    if (APawn* OwnerPawn = Cast<APawn>(GetOwner()))
    {
        // Enhance camera component if available
        if (UCameraComponent* CameraComponent = OwnerPawn->FindComponentByClass<UCameraComponent>())
        {
            // Increase field of view for better vision
            float CurrentFOV = CameraComponent->FieldOfView;
            float EnhancedFOV = CurrentFOV * (1.0f + VisionRangeBonus * 0.2f); // 20% of vision bonus applies to FOV
            CameraComponent->SetFieldOfView(FMath::Clamp(EnhancedFOV, 60.0f, 120.0f));

            // Set timer to reset FOV
            FTimerHandle FOVResetTimer;
            FTimerManagerTimerParameters TimerParams;
            TimerParams.bLoop = false;
            GetWorld()->GetTimerManager().SetTimer(
                FOVResetTimer,
                [CameraComponent, CurrentFOV]()
                {
                    if (IsValid(CameraComponent))
                    {
                        CameraComponent->SetFieldOfView(CurrentFOV);
                    }
                },
                GetVesperSigilDuration(EquippedVesperSigil.VesperSubtype),
                TimerParams
            );
        }

        // Enhance AI perception if available
        if (UAIPerceptionComponent* PerceptionComponent = OwnerPawn->FindComponentByClass<UAIPerceptionComponent>())
        {
            // Get sight sense and enhance its range
            if (UAISenseConfig_Sight* SightConfig = PerceptionComponent->GetSenseConfig<UAISenseConfig_Sight>())
            {
                float OriginalRange = SightConfig->SightRadius;
                float EnhancedRange = OriginalRange * (1.0f + VisionRangeBonus);
                SightConfig->SightRadius = EnhancedRange;

                // Update perception component
                PerceptionComponent->ConfigureSense(*SightConfig);

                // Set timer to reset vision range
                FTimerHandle VisionResetTimer;
                GetWorld()->GetTimerManager().SetTimer(
                    VisionResetTimer,
                    [SightConfig, PerceptionComponent, OriginalRange]()
                    {
                        if (IsValid(SightConfig) && IsValid(PerceptionComponent))
                        {
                            SightConfig->SightRadius = OriginalRange;
                            PerceptionComponent->ConfigureSense(*SightConfig);
                        }
                    },
                    GetVesperSigilDuration(EquippedVesperSigil.VesperSubtype),
                    false
                );
            }
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Vision range enhanced by %.1f%%"), VisionRangeBonus * 100.0f);
}

void UAuracronSigilosBridge::ApplyWindTrailEffect()
{
    if (!GetOwner())
    {
        return;
    }

    // Spawn wind trail effect using UE 5.6 Niagara
    static const FSoftObjectPath WindTrailPath(TEXT("/Game/VFX/Sigils/Vesper/NS_WindTrail.NS_WindTrail"));
    if (UNiagaraSystem* WindTrailVFX = Cast<UNiagaraSystem>(WindTrailPath.TryLoad()))
    {
        UNiagaraComponent* WindTrailComponent = UNiagaraFunctionLibrary::SpawnSystemAttached(
            WindTrailVFX,
            GetOwner()->GetRootComponent(),
            NAME_None,
            FVector(0.0f, 0.0f, -50.0f), // Slightly below character
            FRotator::ZeroRotator,
            EAttachLocation::KeepRelativeOffset,
            false, // Don't auto destroy
            true,  // Auto activate
            ENCPoolMethod::None,
            false  // Don't prewarm for trail
        );

        if (WindTrailComponent)
        {
            // Set wind trail parameters
            float SpeedBonus = 0.4f + (EquippedVesperSigil.Level - 1) * 0.02f;
            // Production Ready: UE 5.6 compatible Niagara API
            WindTrailComponent->SetVariableFloat(FName("SpeedMultiplier"), 1.0f + SpeedBonus);
            WindTrailComponent->SetVariableFloat(FName("TrailIntensity"), 1.0f + (EquippedVesperSigil.Level - 1) * 0.1f);
            WindTrailComponent->SetVariableLinearColor(FName("TrailColor"), FLinearColor(1.0f, 1.0f, 0.0f, 0.8f));

            // Store reference
            ActiveVesperSigilVFX.Add(WindTrailComponent);

            // Set timer to remove trail when effect ends
            FTimerHandle WindTrailTimer;
            GetWorld()->GetTimerManager().SetTimer(
                WindTrailTimer,
                [WindTrailComponent]()
                {
                    if (IsValid(WindTrailComponent))
                    {
                        WindTrailComponent->Deactivate();
                        WindTrailComponent->DestroyComponent();
                    }
                },
                GetVesperSigilDuration(EquippedVesperSigil.VesperSubtype),
                false
            );
        }
    }
}

void UAuracronSigilosBridge::ApplyTrueSightEffect(float Radius)
{
    if (!GetOwner() || !GetWorld())
    {
        return;
    }

    // Apply true sight effect to detect invisible enemies using UE 5.6 collision system
    TArray<FOverlapResult> OverlapResults;
    FCollisionQueryParams QueryParams;
    QueryParams.AddIgnoredActor(GetOwner());
    QueryParams.bTraceComplex = false;

    bool bHasOverlaps = GetWorld()->OverlapMultiByChannel(
        OverlapResults,
        GetOwner()->GetActorLocation(),
        FQuat::Identity,
        ECollisionChannel::ECC_Pawn,
        FCollisionShape::MakeSphere(Radius),
        QueryParams
    );

    if (bHasOverlaps)
    {
        for (const FOverlapResult& Overlap : OverlapResults)
        {
            if (AActor* Target = Overlap.GetActor())
            {
                if (IsEnemyTarget(Target))
                {
                    // Remove stealth/invisibility effects using GameplayTag system
                    if (UAbilitySystemComponent* TargetASC = Target->FindComponentByClass<UAbilitySystemComponent>())
                    {
                        // Remove invisibility tags
                        TargetASC->RemoveLooseGameplayTag(FGameplayTag::RequestGameplayTag(TEXT("Status.Invisible")));
                        TargetASC->RemoveLooseGameplayTag(FGameplayTag::RequestGameplayTag(TEXT("Status.Stealth")));
                        TargetASC->RemoveLooseGameplayTag(FGameplayTag::RequestGameplayTag(TEXT("Status.Hidden")));

                        // Add revealed tag
                        TargetASC->AddLooseGameplayTag(FGameplayTag::RequestGameplayTag(TEXT("Status.Revealed")));

                        // Set timer to remove revealed tag
                        FTimerHandle RevealedTagTimer;
                        GetWorld()->GetTimerManager().SetTimer(
                            RevealedTagTimer,
                            [TargetASC]()
                            {
                                if (IsValid(TargetASC))
                                {
                                    TargetASC->RemoveLooseGameplayTag(FGameplayTag::RequestGameplayTag(TEXT("Status.Revealed")));
                                }
                            },
                            GetVesperSigilDuration(EquippedVesperSigil.VesperSubtype),
                            false
                        );

                        UE_LOG(LogTemp, Log, TEXT("AURACRON: True sight revealed enemy: %s"), *Target->GetName());
                    }
                }
            }
        }
    }
}

// === Additional Area Effect Methods ===

void UAuracronSigilosBridge::ApplyShadowFieldEffect(float Radius, float VisionReduction, float AccuracyReduction)
{
    if (!GetOwner() || !GetWorld())
    {
        return;
    }

    // Find enemies within shadow field radius
    TArray<FOverlapResult> OverlapResults;
    FCollisionQueryParams QueryParams;
    QueryParams.AddIgnoredActor(GetOwner());

    bool bHasOverlaps = GetWorld()->OverlapMultiByChannel(
        OverlapResults,
        GetOwner()->GetActorLocation(),
        FQuat::Identity,
        ECollisionChannel::ECC_Pawn,
        FCollisionShape::MakeSphere(Radius),
        QueryParams
    );

    if (bHasOverlaps)
    {
        for (const FOverlapResult& Overlap : OverlapResults)
        {
            if (AActor* Enemy = Overlap.GetActor())
            {
                if (IsEnemyTarget(Enemy))
                {
                    if (UAbilitySystemComponent* EnemyASC = Enemy->FindComponentByClass<UAbilitySystemComponent>())
                    {
                        // Apply vision reduction effect
                        FGameplayEffectContextHandle EffectContext = AbilitySystemComponent->MakeEffectContext();
                        EffectContext.AddSourceObject(this);

                        static const FSoftClassPath VisionReductionPath(TEXT("/Game/GameplayEffects/Debuffs/GE_VisionReduction.GE_VisionReduction_C"));
                        if (TSubclassOf<UGameplayEffect> VisionReductionEffect = VisionReductionPath.TryLoadClass<UGameplayEffect>())
                        {
                            FGameplayEffectSpec VisionSpec(VisionReductionEffect.GetDefaultObject(), EffectContext, static_cast<float>(EquippedRuinSigil.Level));
                            VisionSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Debuff.VisionReduction")), VisionReduction);
                            VisionSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Debuff.AccuracyReduction")), AccuracyReduction);
                            VisionSpec.SetDuration(GetRuinSigilDuration(EquippedRuinSigil.RuinSubtype), false);

                            EnemyASC->ApplyGameplayEffectSpecToSelf(VisionSpec);
                        }
                    }
                }
            }
        }
    }
}

void UAuracronSigilosBridge::ApplyCorrosiveAuraEffect(float Radius, float ArmorReduction, float ResistanceReduction)
{
    if (!GetOwner() || !GetWorld())
    {
        return;
    }

    // Find enemies within corrosive aura radius
    TArray<FOverlapResult> OverlapResults;
    FCollisionQueryParams QueryParams;
    QueryParams.AddIgnoredActor(GetOwner());

    bool bHasOverlaps = GetWorld()->OverlapMultiByChannel(
        OverlapResults,
        GetOwner()->GetActorLocation(),
        FQuat::Identity,
        ECollisionChannel::ECC_Pawn,
        FCollisionShape::MakeSphere(Radius),
        QueryParams
    );

    if (bHasOverlaps)
    {
        for (const FOverlapResult& Overlap : OverlapResults)
        {
            if (AActor* Enemy = Overlap.GetActor())
            {
                if (IsEnemyTarget(Enemy))
                {
                    if (UAbilitySystemComponent* EnemyASC = Enemy->FindComponentByClass<UAbilitySystemComponent>())
                    {
                        // Apply corrosive debuff
                        FGameplayEffectContextHandle EffectContext = AbilitySystemComponent->MakeEffectContext();
                        EffectContext.AddSourceObject(this);

                        static const FSoftClassPath CorrosiveDebuffPath(TEXT("/Game/GameplayEffects/Debuffs/GE_CorrosiveDebuff.GE_CorrosiveDebuff_C"));
                        if (TSubclassOf<UGameplayEffect> CorrosiveDebuffEffect = CorrosiveDebuffPath.TryLoadClass<UGameplayEffect>())
                        {
                            FGameplayEffectSpec CorrosiveSpec(CorrosiveDebuffEffect.GetDefaultObject(), EffectContext, static_cast<float>(EquippedRuinSigil.Level));
                            CorrosiveSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Debuff.ArmorReduction")), ArmorReduction);
                            CorrosiveSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Debuff.ResistanceReduction")), ResistanceReduction);
                            CorrosiveSpec.SetDuration(GetRuinSigilDuration(EquippedRuinSigil.RuinSubtype), false);

                            EnemyASC->ApplyGameplayEffectSpecToSelf(CorrosiveSpec);
                        }
                    }
                }
            }
        }
    }
}

void UAuracronSigilosBridge::ApplyExecutionEffect(float Radius, float ExecutionThreshold)
{
    if (!GetOwner() || !GetWorld())
    {
        return;
    }

    // Find enemies within execution radius
    TArray<FOverlapResult> OverlapResults;
    FCollisionQueryParams QueryParams;
    QueryParams.AddIgnoredActor(GetOwner());

    bool bHasOverlaps = GetWorld()->OverlapMultiByChannel(
        OverlapResults,
        GetOwner()->GetActorLocation(),
        FQuat::Identity,
        ECollisionChannel::ECC_Pawn,
        FCollisionShape::MakeSphere(Radius),
        QueryParams
    );

    if (bHasOverlaps)
    {
        for (const FOverlapResult& Overlap : OverlapResults)
        {
            if (AActor* Enemy = Overlap.GetActor())
            {
                if (IsEnemyTarget(Enemy))
                {
                    if (UAbilitySystemComponent* EnemyASC = Enemy->FindComponentByClass<UAbilitySystemComponent>())
                    {
                        // Check current health percentage using UE 5.6 attribute system
                        float CurrentHealth = EnemyASC->GetNumericAttribute(UAuracronSigilAttributeSet::GetHealthAttribute());
                        float MaxHealth = EnemyASC->GetNumericAttribute(UAuracronSigilAttributeSet::GetMaxHealthAttribute());

                        if (MaxHealth > 0.0f)
                        {
                            float HealthPercentage = CurrentHealth / MaxHealth;

                            if (HealthPercentage <= ExecutionThreshold)
                            {
                                // Execute enemy using instant death effect
                                FGameplayEffectContextHandle EffectContext = AbilitySystemComponent->MakeEffectContext();
                                EffectContext.AddSourceObject(this);
                                EffectContext.AddHitResult(FHitResult(), true);

                                static const FSoftClassPath ExecutionPath(TEXT("/Game/GameplayEffects/Special/GE_InstantExecution.GE_InstantExecution_C"));
                                if (TSubclassOf<UGameplayEffect> ExecutionEffect = ExecutionPath.TryLoadClass<UGameplayEffect>())
                                {
                                    FGameplayEffectSpec ExecutionSpec(ExecutionEffect.GetDefaultObject(), EffectContext, static_cast<float>(EquippedRuinSigil.Level));
                                    ExecutionSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Damage.Execution")), MaxHealth * 2.0f); // Overkill damage

                                    EnemyASC->ApplyGameplayEffectSpecToSelf(ExecutionSpec);

                                    UE_LOG(LogTemp, Log, TEXT("AURACRON: Executed enemy: %s (%.1f%% health)"),
                                        *Enemy->GetName(), HealthPercentage * 100.0f);
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

void UAuracronSigilosBridge::ApplyEnergyFieldEffect(float Radius, float ManaPerSecond, float CostReduction)
{
    if (!GetOwner() || !GetWorld())
    {
        return;
    }

    // Find allies within energy field radius
    TArray<FOverlapResult> OverlapResults;
    FCollisionQueryParams QueryParams;

    bool bHasOverlaps = GetWorld()->OverlapMultiByChannel(
        OverlapResults,
        GetOwner()->GetActorLocation(),
        FQuat::Identity,
        ECollisionChannel::ECC_Pawn,
        FCollisionShape::MakeSphere(Radius),
        QueryParams
    );

    if (bHasOverlaps)
    {
        for (const FOverlapResult& Overlap : OverlapResults)
        {
            if (AActor* Ally = Overlap.GetActor())
            {
                if (IsAllyTarget(Ally))
                {
                    if (UAbilitySystemComponent* AllyASC = Ally->FindComponentByClass<UAbilitySystemComponent>())
                    {
                        // Apply energy field effect
                        FGameplayEffectContextHandle EffectContext = AbilitySystemComponent->MakeEffectContext();
                        EffectContext.AddSourceObject(this);

                        static const FSoftClassPath EnergyFieldPath(TEXT("/Game/GameplayEffects/Support/GE_EnergyField.GE_EnergyField_C"));
                        if (TSubclassOf<UGameplayEffect> EnergyFieldEffect = EnergyFieldPath.TryLoadClass<UGameplayEffect>())
                        {
                            FGameplayEffectSpec EnergySpec(EnergyFieldEffect.GetDefaultObject(), EffectContext, static_cast<float>(EquippedVesperSigil.Level));
                            EnergySpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Bonus.ManaRegen")), ManaPerSecond);
                            EnergySpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Bonus.CostReduction")), CostReduction);
                            EnergySpec.SetDuration(GetVesperSigilDuration(EquippedVesperSigil.VesperSubtype), false);

                            AllyASC->ApplyGameplayEffectSpecToSelf(EnergySpec);
                        }
                    }
                }
            }
        }
    }
}

// === Core Archetype Generation Method ===

FAuracronSigilArchetype UAuracronSigilosBridge::GenerateArchetype(EAuracronAegisSigilType AegisType, EAuracronRuinSigilType RuinType, EAuracronVesperSigilType VesperType) const
{
    FAuracronSigilArchetype NewArchetype;

    // Set component types
    NewArchetype.AegisComponent = AegisType;
    NewArchetype.RuinComponent = RuinType;
    NewArchetype.VesperComponent = VesperType;

    // Generate unique archetype tag using UE 5.6 GameplayTag system
    NewArchetype.ArchetypeTag = AuracronSigilTags::GenerateArchetypeTag(AegisType, RuinType, VesperType);

    // Generate archetype name and description
    NewArchetype.ArchetypeName = GenerateArchetypeName(AegisType, RuinType, VesperType);
    NewArchetype.ArchetypeDescription = GenerateArchetypeDescription(AegisType, RuinType, VesperType);

    // Calculate archetype properties using modern algorithms
    NewArchetype.PowerMultiplier = CalculateArchetypePowerMultiplier();
    NewArchetype.CooldownReduction = CalculateArchetypeCooldownReduction();
    NewArchetype.EnergyEfficiency = CalculateArchetypeEnergyEfficiency();

    // Calculate required level based on sigil complexity
    NewArchetype.RequiredLevel = CalculateArchetypeRequiredLevel(AegisType, RuinType, VesperType);

    // Set archetype colors using advanced color blending
    NewArchetype.PrimaryColor = BlendSigilColors();
    NewArchetype.SecondaryColor = NewArchetype.PrimaryColor * 0.7f; // Darker secondary

    // Assign fusion ability based on archetype category
    NewArchetype.FusionAbility = GetArchetypeFusionAbility(AegisType, RuinType, VesperType);

    // Assign passive effects based on sigil combination
    NewArchetype.ArchetypePassiveEffects = GetArchetypePassiveEffects(AegisType, RuinType, VesperType);

    // Assign VFX and audio assets
    NewArchetype.ArchetypeVFX = GetArchetypeVFXAsset(AegisType, RuinType, VesperType);
    NewArchetype.ArchetypeAudio = GetArchetypeAudioAsset(AegisType, RuinType, VesperType);

    return NewArchetype;
}

int32 UAuracronSigilosBridge::CalculateArchetypeRequiredLevel(EAuracronAegisSigilType AegisType, EAuracronRuinSigilType RuinType, EAuracronVesperSigilType VesperType) const
{
    // Calculate required level based on sigil complexity using UE 5.6 math utilities
    int32 BaseLevel = 1;

    // Aegis complexity contribution
    switch (AegisType)
    {
        case EAuracronAegisSigilType::Primordial:
            BaseLevel += 0;
            break;
        case EAuracronAegisSigilType::Cristalino:
            BaseLevel += 3;
            break;
        case EAuracronAegisSigilType::Temporal:
            BaseLevel += 5;
            break;
        case EAuracronAegisSigilType::Espectral:
            BaseLevel += 7;
            break;
        case EAuracronAegisSigilType::Absoluto:
            BaseLevel += 15; // Legendary tier
            break;
        default:
            break;
    }

    // Ruin complexity contribution
    switch (RuinType)
    {
        case EAuracronRuinSigilType::Flamejante:
            BaseLevel += 2;
            break;
        case EAuracronRuinSigilType::Gelido:
            BaseLevel += 1;
            break;
        case EAuracronRuinSigilType::Sombrio:
            BaseLevel += 6;
            break;
        case EAuracronRuinSigilType::Corrosivo:
            BaseLevel += 4;
            break;
        case EAuracronRuinSigilType::Aniquilador:
            BaseLevel += 18; // Legendary tier
            break;
        default:
            break;
    }

    // Vesper complexity contribution
    switch (VesperType)
    {
        case EAuracronVesperSigilType::Curativo:
            BaseLevel += 1;
            break;
        case EAuracronVesperSigilType::Energetico:
            BaseLevel += 0;
            break;
        case EAuracronVesperSigilType::Velocidade:
            BaseLevel += 3;
            break;
        case EAuracronVesperSigilType::Visao:
            BaseLevel += 2;
            break;
        case EAuracronVesperSigilType::Teleporte:
            BaseLevel += 8;
            break;
        case EAuracronVesperSigilType::Temporal:
            BaseLevel += 12; // High complexity
            break;
        default:
            break;
    }

    // Apply synergy complexity modifier
    float SynergyComplexity = FMath::Abs(CalculateSynergyBonus()) * 5.0f; // Synergy adds complexity
    BaseLevel += FMath::RoundToInt(SynergyComplexity);

    // Clamp to reasonable bounds
    return FMath::Clamp(BaseLevel, 1, 60);
}

TSubclassOf<UGameplayAbility> UAuracronSigilosBridge::GetArchetypeFusionAbility(EAuracronAegisSigilType AegisType, EAuracronRuinSigilType RuinType, EAuracronVesperSigilType VesperType) const
{
    // Generate fusion ability path based on archetype combination using UE 5.6 asset management
    FString AbilityPath = FString::Printf(TEXT("/Game/GameplayAbilities/Archetypes/GA_Archetype_%d_%d_%d.GA_Archetype_%d_%d_%d_C"),
        static_cast<int32>(AegisType), static_cast<int32>(RuinType), static_cast<int32>(VesperType),
        static_cast<int32>(AegisType), static_cast<int32>(RuinType), static_cast<int32>(VesperType));

    FSoftClassPath AbilitySoftPath(AbilityPath);
    TSubclassOf<UGameplayAbility> FusionAbility = AbilitySoftPath.TryLoadClass<UGameplayAbility>();

    // Fallback to category-based abilities if specific combination doesn't exist
    if (!FusionAbility)
    {
        FString CategoryPath;
        FString ArchetypeCategory = GetArchetypeCategory(FAuracronSigilArchetype()); // Temporary archetype for category calculation

        if (ArchetypeCategory == TEXT("Guardian") || ArchetypeCategory == TEXT("Protector"))
        {
            CategoryPath = TEXT("/Game/GameplayAbilities/Categories/GA_DefensiveFusion.GA_DefensiveFusion_C");
        }
        else if (ArchetypeCategory == TEXT("Destroyer") || ArchetypeCategory == TEXT("Assassin") || ArchetypeCategory == TEXT("Warrior"))
        {
            CategoryPath = TEXT("/Game/GameplayAbilities/Categories/GA_OffensiveFusion.GA_OffensiveFusion_C");
        }
        else if (ArchetypeCategory == TEXT("Healer") || ArchetypeCategory == TEXT("Controller") || ArchetypeCategory == TEXT("Hybrid"))
        {
            CategoryPath = TEXT("/Game/GameplayAbilities/Categories/GA_SupportFusion.GA_SupportFusion_C");
        }
        else
        {
            CategoryPath = TEXT("/Game/GameplayAbilities/Categories/GA_BalancedFusion.GA_BalancedFusion_C");
        }

        FSoftClassPath CategorySoftPath(CategoryPath);
        FusionAbility = CategorySoftPath.TryLoadClass<UGameplayAbility>();
    }

    return FusionAbility;
}

TArray<TSubclassOf<UGameplayEffect>> UAuracronSigilosBridge::GetArchetypePassiveEffects(EAuracronAegisSigilType AegisType, EAuracronRuinSigilType RuinType, EAuracronVesperSigilType VesperType) const
{
    TArray<TSubclassOf<UGameplayEffect>> PassiveEffects;

    // Add base archetype passive effect
    static const FSoftClassPath BasePassivePath(TEXT("/Game/GameplayEffects/Archetypes/GE_ArchetypeBasePassive.GE_ArchetypeBasePassive_C"));
    if (TSubclassOf<UGameplayEffect> BasePassive = BasePassivePath.TryLoadClass<UGameplayEffect>())
    {
        PassiveEffects.Add(BasePassive);
    }

    // Add Aegis-specific passive effects
    switch (AegisType)
    {
        case EAuracronAegisSigilType::Cristalino:
            {
                static const FSoftClassPath ReflectionPassivePath(TEXT("/Game/GameplayEffects/Passives/GE_ReflectionPassive.GE_ReflectionPassive_C"));
                if (TSubclassOf<UGameplayEffect> ReflectionPassive = ReflectionPassivePath.TryLoadClass<UGameplayEffect>())
                {
                    PassiveEffects.Add(ReflectionPassive);
                }
            }
            break;
        case EAuracronAegisSigilType::Temporal:
            {
                static const FSoftClassPath TimePassivePath(TEXT("/Game/GameplayEffects/Passives/GE_TimeResistancePassive.GE_TimeResistancePassive_C"));
                if (TSubclassOf<UGameplayEffect> TimePassive = TimePassivePath.TryLoadClass<UGameplayEffect>())
                {
                    PassiveEffects.Add(TimePassive);
                }
            }
            break;
        case EAuracronAegisSigilType::Espectral:
            {
                static const FSoftClassPath MagicPassivePath(TEXT("/Game/GameplayEffects/Passives/GE_MagicResistancePassive.GE_MagicResistancePassive_C"));
                if (TSubclassOf<UGameplayEffect> MagicPassive = MagicPassivePath.TryLoadClass<UGameplayEffect>())
                {
                    PassiveEffects.Add(MagicPassive);
                }
            }
            break;
        case EAuracronAegisSigilType::Absoluto:
            {
                static const FSoftClassPath DivinePassivePath(TEXT("/Game/GameplayEffects/Passives/GE_DivineProtectionPassive.GE_DivineProtectionPassive_C"));
                if (TSubclassOf<UGameplayEffect> DivinePassive = DivinePassivePath.TryLoadClass<UGameplayEffect>())
                {
                    PassiveEffects.Add(DivinePassive);
                }
            }
            break;
        default:
            break;
    }

    // Add Ruin-specific passive effects
    switch (RuinType)
    {
        case EAuracronRuinSigilType::Flamejante:
            {
                static const FSoftClassPath FirePassivePath(TEXT("/Game/GameplayEffects/Passives/GE_FireMasteryPassive.GE_FireMasteryPassive_C"));
                if (TSubclassOf<UGameplayEffect> FirePassive = FirePassivePath.TryLoadClass<UGameplayEffect>())
                {
                    PassiveEffects.Add(FirePassive);
                }
            }
            break;
        case EAuracronRuinSigilType::Sombrio:
            {
                static const FSoftClassPath ShadowPassivePath(TEXT("/Game/GameplayEffects/Passives/GE_ShadowMasteryPassive.GE_ShadowMasteryPassive_C"));
                if (TSubclassOf<UGameplayEffect> ShadowPassive = ShadowPassivePath.TryLoadClass<UGameplayEffect>())
                {
                    PassiveEffects.Add(ShadowPassive);
                }
            }
            break;
        case EAuracronRuinSigilType::Aniquilador:
            {
                static const FSoftClassPath VoidPassivePath(TEXT("/Game/GameplayEffects/Passives/GE_VoidMasteryPassive.GE_VoidMasteryPassive_C"));
                if (TSubclassOf<UGameplayEffect> VoidPassive = VoidPassivePath.TryLoadClass<UGameplayEffect>())
                {
                    PassiveEffects.Add(VoidPassive);
                }
            }
            break;
        default:
            break;
    }

    // Add Vesper-specific passive effects
    switch (VesperType)
    {
        case EAuracronVesperSigilType::Teleporte:
            {
                static const FSoftClassPath MobilityPassivePath(TEXT("/Game/GameplayEffects/Passives/GE_MobilityMasteryPassive.GE_MobilityMasteryPassive_C"));
                if (TSubclassOf<UGameplayEffect> MobilityPassive = MobilityPassivePath.TryLoadClass<UGameplayEffect>())
                {
                    PassiveEffects.Add(MobilityPassive);
                }
            }
            break;
        case EAuracronVesperSigilType::Temporal:
            {
                static const FSoftClassPath ChronoPassivePath(TEXT("/Game/GameplayEffects/Passives/GE_ChronoMasteryPassive.GE_ChronoMasteryPassive_C"));
                if (TSubclassOf<UGameplayEffect> ChronoPassive = ChronoPassivePath.TryLoadClass<UGameplayEffect>())
                {
                    PassiveEffects.Add(ChronoPassive);
                }
            }
            break;
        default:
            break;
    }

    // Add legendary combination bonuses
    if (IsLegendaryArchetypeCombination(AegisType, RuinType, VesperType))
    {
        static const FSoftClassPath LegendaryPassivePath(TEXT("/Game/GameplayEffects/Passives/GE_LegendaryArchetypePassive.GE_LegendaryArchetypePassive_C"));
        if (TSubclassOf<UGameplayEffect> LegendaryPassive = LegendaryPassivePath.TryLoadClass<UGameplayEffect>())
        {
            PassiveEffects.Add(LegendaryPassive);
        }
    }

    return PassiveEffects;
}

TSoftObjectPtr<UNiagaraSystem> UAuracronSigilosBridge::GetArchetypeVFXAsset(EAuracronAegisSigilType AegisType, EAuracronRuinSigilType RuinType, EAuracronVesperSigilType VesperType) const
{
    // Generate VFX asset path based on archetype combination
    FString VFXPath = FString::Printf(TEXT("/Game/VFX/Archetypes/NS_Archetype_%d_%d_%d.NS_Archetype_%d_%d_%d"),
        static_cast<int32>(AegisType), static_cast<int32>(RuinType), static_cast<int32>(VesperType),
        static_cast<int32>(AegisType), static_cast<int32>(RuinType), static_cast<int32>(VesperType));

    // Production Ready: UE 5.6 compatible TSoftObjectPtr construction using FSoftObjectPath
    TSoftObjectPtr<UNiagaraSystem> VFXAsset = TSoftObjectPtr<UNiagaraSystem>(FSoftObjectPath(VFXPath));

    // Fallback to category-based VFX if specific combination doesn't exist
    // Production Ready: UE 5.6 compatible TSoftObjectPtr validation
    if (VFXAsset.IsNull())
    {
        FString CategoryVFXPath;

        // Determine VFX category based on dominant element
        if (AegisType == EAuracronAegisSigilType::Absoluto || RuinType == EAuracronRuinSigilType::Aniquilador || VesperType == EAuracronVesperSigilType::Temporal)
        {
            CategoryVFXPath = TEXT("/Game/VFX/Archetypes/Categories/NS_LegendaryArchetype.NS_LegendaryArchetype");
        }
        else if (AegisType == EAuracronAegisSigilType::Temporal && VesperType == EAuracronVesperSigilType::Temporal)
        {
            CategoryVFXPath = TEXT("/Game/VFX/Archetypes/Categories/NS_TemporalMaster.NS_TemporalMaster");
        }
        else if (RuinType == EAuracronRuinSigilType::Sombrio && VesperType == EAuracronVesperSigilType::Teleporte)
        {
            CategoryVFXPath = TEXT("/Game/VFX/Archetypes/Categories/NS_ShadowAssassin.NS_ShadowAssassin");
        }
        else if (AegisType == EAuracronAegisSigilType::Cristalino && VesperType == EAuracronVesperSigilType::Curativo)
        {
            CategoryVFXPath = TEXT("/Game/VFX/Archetypes/Categories/NS_CrystalGuardian.NS_CrystalGuardian");
        }
        else
        {
            // Default archetype VFX
            CategoryVFXPath = TEXT("/Game/VFX/Archetypes/Categories/NS_DefaultArchetype.NS_DefaultArchetype");
        }

        VFXAsset = TSoftObjectPtr<UNiagaraSystem>(FSoftObjectPath(CategoryVFXPath));
    }

    return VFXAsset;
}

TSoftObjectPtr<UMetaSoundSource> UAuracronSigilosBridge::GetArchetypeAudioAsset(EAuracronAegisSigilType AegisType, EAuracronRuinSigilType RuinType, EAuracronVesperSigilType VesperType) const
{
    // Generate audio asset path based on archetype combination
    FString AudioPath = FString::Printf(TEXT("/Game/Audio/Archetypes/MS_Archetype_%d_%d_%d.MS_Archetype_%d_%d_%d"),
        static_cast<int32>(AegisType), static_cast<int32>(RuinType), static_cast<int32>(VesperType),
        static_cast<int32>(AegisType), static_cast<int32>(RuinType), static_cast<int32>(VesperType));

    // Production Ready: UE 5.6 compatible TSoftObjectPtr construction using FSoftObjectPath
    TSoftObjectPtr<UMetaSoundSource> AudioAsset = TSoftObjectPtr<UMetaSoundSource>(FSoftObjectPath(AudioPath));

    // Fallback to category-based audio if specific combination doesn't exist
    if (AudioAsset.IsNull())
    {
        FString CategoryAudioPath;

        // Determine audio category based on dominant element
        if (AegisType == EAuracronAegisSigilType::Absoluto || RuinType == EAuracronRuinSigilType::Aniquilador || VesperType == EAuracronVesperSigilType::Temporal)
        {
            CategoryAudioPath = TEXT("/Game/Audio/Archetypes/Categories/MS_LegendaryArchetype.MS_LegendaryArchetype");
        }
        else if (AegisType == EAuracronAegisSigilType::Temporal && VesperType == EAuracronVesperSigilType::Temporal)
        {
            CategoryAudioPath = TEXT("/Game/Audio/Archetypes/Categories/MS_TemporalMaster.MS_TemporalMaster");
        }
        else if (RuinType == EAuracronRuinSigilType::Sombrio && VesperType == EAuracronVesperSigilType::Teleporte)
        {
            CategoryAudioPath = TEXT("/Game/Audio/Archetypes/Categories/MS_ShadowAssassin.MS_ShadowAssassin");
        }
        else if (AegisType == EAuracronAegisSigilType::Cristalino && VesperType == EAuracronVesperSigilType::Curativo)
        {
            CategoryAudioPath = TEXT("/Game/Audio/Archetypes/Categories/MS_CrystalGuardian.MS_CrystalGuardian");
        }
        else
        {
            // Default archetype audio
            CategoryAudioPath = TEXT("/Game/Audio/Archetypes/Categories/MS_DefaultArchetype.MS_DefaultArchetype");
        }

        AudioAsset = TSoftObjectPtr<UMetaSoundSource>(FSoftObjectPath(CategoryAudioPath));
    }

    return AudioAsset;
}

bool UAuracronSigilosBridge::IsLegendaryArchetypeCombination(EAuracronAegisSigilType AegisType, EAuracronRuinSigilType RuinType, EAuracronVesperSigilType VesperType) const
{
    // Define legendary archetype combinations using UE 5.6 efficient comparison
    struct FLegendaryCombination
    {
        EAuracronAegisSigilType Aegis;
        EAuracronRuinSigilType Ruin;
        EAuracronVesperSigilType Vesper;
    };

    // Static array of legendary combinations for performance
    static const TArray<FLegendaryCombination> LegendaryCombinations = {
        // Destruidor Absoluto
        {EAuracronAegisSigilType::Absoluto, EAuracronRuinSigilType::Aniquilador, EAuracronVesperSigilType::Temporal},

        // Mestre Temporal
        {EAuracronAegisSigilType::Temporal, EAuracronRuinSigilType::Sombrio, EAuracronVesperSigilType::Temporal},

        // Guardião Cristalino
        {EAuracronAegisSigilType::Cristalino, EAuracronRuinSigilType::Flamejante, EAuracronVesperSigilType::Curativo},

        // Assassino Sombrio
        {EAuracronAegisSigilType::Espectral, EAuracronRuinSigilType::Sombrio, EAuracronVesperSigilType::Teleporte},

        // Senhor do Gelo
        {EAuracronAegisSigilType::Cristalino, EAuracronRuinSigilType::Gelido, EAuracronVesperSigilType::Temporal},

        // Destruidor Corrosivo
        {EAuracronAegisSigilType::Espectral, EAuracronRuinSigilType::Corrosivo, EAuracronVesperSigilType::Energetico},

        // Guardião Temporal
        {EAuracronAegisSigilType::Temporal, EAuracronRuinSigilType::Corrosivo, EAuracronVesperSigilType::Curativo},

        // Mestre da Velocidade
        {EAuracronAegisSigilType::Primordial, EAuracronRuinSigilType::Flamejante, EAuracronVesperSigilType::Velocidade}
    };

    // Check if current combination matches any legendary combination
    for (const FLegendaryCombination& Legendary : LegendaryCombinations)
    {
        if (Legendary.Aegis == AegisType && Legendary.Ruin == RuinType && Legendary.Vesper == VesperType)
        {
            return true;
        }
    }

    return false;
}

FText UAuracronSigilosBridge::GenerateArchetypeDescription(EAuracronAegisSigilType AegisType, EAuracronRuinSigilType RuinType, EAuracronVesperSigilType VesperType) const
{
    // Generate dynamic archetype descriptions using UE 5.6 text formatting
    FString AegisDescription = GetAegisSigilDescription(AegisType);
    FString RuinDescription = GetRuinSigilDescription(RuinType);
    FString VesperDescription = GetVesperSigilDescription(VesperType);

    // Check for legendary combinations first
    if (IsLegendaryArchetypeCombination(AegisType, RuinType, VesperType))
    {
        if (AegisType == EAuracronAegisSigilType::Absoluto && RuinType == EAuracronRuinSigilType::Aniquilador && VesperType == EAuracronVesperSigilType::Temporal)
        {
            return FText::FromString(TEXT("O arquétipo supremo da destruição. Combina invulnerabilidade absoluta com poder aniquilador e controle temporal. Capaz de parar o tempo, tornar-se intocável e liberar devastação apocalíptica."));
        }
        else if (AegisType == EAuracronAegisSigilType::Temporal && RuinType == EAuracronRuinSigilType::Sombrio && VesperType == EAuracronVesperSigilType::Temporal)
        {
            return FText::FromString(TEXT("Mestre absoluto do tempo e das sombras. Manipula o fluxo temporal enquanto se move através das trevas, controlando o ritmo da batalha com precisão sobrenatural."));
        }
        else if (AegisType == EAuracronAegisSigilType::Cristalino && RuinType == EAuracronRuinSigilType::Flamejante && VesperType == EAuracronVesperSigilType::Curativo)
        {
            return FText::FromString(TEXT("Guardião cristalino que reflete ataques enquanto canaliza chamas curativas. Protege aliados com escudos de cristal e regeneração flamejante."));
        }
        else if (AegisType == EAuracronAegisSigilType::Espectral && RuinType == EAuracronRuinSigilType::Sombrio && VesperType == EAuracronVesperSigilType::Teleporte)
        {
            return FText::FromString(TEXT("Assassino das sombras espectrais. Move-se entre dimensões, absorve magia inimiga e ataca das trevas com teleportação instantânea."));
        }
    }

    // Generate dynamic description for non-legendary combinations
    FString BaseDescription;

    // Determine primary archetype role
    FString ArchetypeCategory = GetArchetypeCategory(FAuracronSigilArchetype()); // Temporary for category

    if (ArchetypeCategory == TEXT("Guardian") || ArchetypeCategory == TEXT("Protector"))
    {
        BaseDescription = FString::Printf(TEXT("Um guardião que combina %s com %s, apoiado por %s. Especializado em proteção e resistência."),
            *AegisDescription, *RuinDescription, *VesperDescription);
    }
    else if (ArchetypeCategory == TEXT("Destroyer") || ArchetypeCategory == TEXT("Assassin"))
    {
        BaseDescription = FString::Printf(TEXT("Um destruidor que utiliza %s para se proteger enquanto libera %s, potencializado por %s. Foco em eliminação rápida."),
            *AegisDescription, *RuinDescription, *VesperDescription);
    }
    else if (ArchetypeCategory == TEXT("Warrior"))
    {
        BaseDescription = FString::Printf(TEXT("Um guerreiro equilibrado que combina %s, %s e %s em harmonia. Versátil em combate e adaptável."),
            *AegisDescription, *RuinDescription, *VesperDescription);
    }
    else if (ArchetypeCategory == TEXT("Healer") || ArchetypeCategory == TEXT("Controller"))
    {
        BaseDescription = FString::Printf(TEXT("Um especialista em suporte que usa %s para proteção, %s para controle e %s para assistência. Foco em apoio tático."),
            *AegisDescription, *RuinDescription, *VesperDescription);
    }
    else // Hybrid
    {
        BaseDescription = FString::Printf(TEXT("Um híbrido único que mescla %s, %s e %s de forma inovadora. Combinação experimental com potencial inexplorado."),
            *AegisDescription, *RuinDescription, *VesperDescription);
    }

    // Add synergy information if significant
    float SynergyBonus = CalculateSynergyBonus();
    if (SynergyBonus > 0.2f)
    {
        BaseDescription += TEXT(" Possui sinergia elemental excepcional que amplifica significativamente seus poderes.");
    }
    else if (SynergyBonus < -0.1f)
    {
        BaseDescription += TEXT(" Elementos conflitantes exigem maestria para equilibrar as forças opostas.");
    }

    return FText::FromString(BaseDescription);
}

FString UAuracronSigilosBridge::GetAegisSigilDescription(EAuracronAegisSigilType AegisType) const
{
    switch (AegisType)
    {
        case EAuracronAegisSigilType::Primordial:
            return TEXT("proteção primordial");
        case EAuracronAegisSigilType::Cristalino:
            return TEXT("escudos cristalinos reflexivos");
        case EAuracronAegisSigilType::Temporal:
            return TEXT("manipulação temporal defensiva");
        case EAuracronAegisSigilType::Espectral:
            return TEXT("absorção mágica espectral");
        case EAuracronAegisSigilType::Absoluto:
            return TEXT("invulnerabilidade absoluta");
        default:
            return TEXT("proteção desconhecida");
    }
}

FString UAuracronSigilosBridge::GetRuinSigilDescription(EAuracronRuinSigilType RuinType) const
{
    switch (RuinType)
    {
        case EAuracronRuinSigilType::Flamejante:
            return TEXT("chamas devastadoras");
        case EAuracronRuinSigilType::Gelido:
            return TEXT("gelo paralisante");
        case EAuracronRuinSigilType::Sombrio:
            return TEXT("sombras cegantes");
        case EAuracronRuinSigilType::Corrosivo:
            return TEXT("ácido corrosivo");
        case EAuracronRuinSigilType::Aniquilador:
            return TEXT("aniquilação total");
        default:
            return TEXT("destruição desconhecida");
    }
}

FString UAuracronSigilosBridge::GetVesperSigilDescription(EAuracronVesperSigilType VesperType) const
{
    switch (VesperType)
    {
        case EAuracronVesperSigilType::Curativo:
            return TEXT("regeneração vital");
        case EAuracronVesperSigilType::Energetico:
            return TEXT("eficiência energética");
        case EAuracronVesperSigilType::Velocidade:
            return TEXT("velocidade sobrenatural");
        case EAuracronVesperSigilType::Visao:
            return TEXT("visão aprimorada");
        case EAuracronVesperSigilType::Teleporte:
            return TEXT("teleportação instantânea");
        case EAuracronVesperSigilType::Temporal:
            return TEXT("controle temporal");
        default:
            return TEXT("suporte desconhecido");
    }
}

// === Sigil Progression System Implementation ===

int32 UAuracronSigilosBridge::CalculateSigilLevel(int32 Experience) const
{
    // Use UE 5.6 math utilities for level calculation with exponential curve
    if (Experience <= 0)
    {
        return 1;
    }

    // Exponential experience curve: Level = floor(sqrt(Experience / 100)) + 1
    // This creates a smooth progression where each level requires more experience
    float ExperienceFloat = static_cast<float>(Experience);
    float LevelFloat = FMath::Sqrt(ExperienceFloat / 100.0f) + 1.0f;
    int32 CalculatedLevel = FMath::FloorToInt(LevelFloat);

    // Clamp to maximum level
    return FMath::Clamp(CalculatedLevel, 1, 20);
}

int32 UAuracronSigilosBridge::GetExperienceRequiredForLevel(int32 Level) const
{
    // Calculate experience required for a specific level using inverse formula
    if (Level <= 1)
    {
        return 0;
    }

    // Inverse of level calculation: Experience = ((Level - 1)^2) * 100
    float LevelFloat = static_cast<float>(Level - 1);
    float RequiredExperience = LevelFloat * LevelFloat * 100.0f;

    return FMath::RoundToInt(RequiredExperience);
}

void UAuracronSigilosBridge::AddSigilExperience(EAuracronSigiloType SigilType, int32 ExperienceAmount)
{
    if (ExperienceAmount <= 0)
    {
        return;
    }

    FAuracronEquippedSigil* TargetSigil = nullptr;
    int32 OldLevel = 1;

    // Get target sigil reference using UE 5.6 efficient switching
    switch (SigilType)
    {
        case EAuracronSigiloType::Aegis:
            TargetSigil = &EquippedAegisSigil;
            OldLevel = EquippedAegisSigil.Level;
            break;
        case EAuracronSigiloType::Ruin:
            TargetSigil = &EquippedRuinSigil;
            OldLevel = EquippedRuinSigil.Level;
            break;
        case EAuracronSigiloType::Vesper:
            TargetSigil = &EquippedVesperSigil;
            OldLevel = EquippedVesperSigil.Level;
            break;
        default:
            return;
    }

    if (!TargetSigil)
    {
        return;
    }

    // Add experience with thread safety
    FScopeLock Lock(&SigiloMutex);

    TargetSigil->Experience += ExperienceAmount;
    int32 NewLevel = CalculateSigilLevel(TargetSigil->Experience);

    // Check for level up
    if (NewLevel > OldLevel)
    {
        TargetSigil->Level = NewLevel;

        // Broadcast level up event
        OnSigilLevelUp.Broadcast(SigilType, NewLevel, TargetSigil->Experience);

        // Apply level up bonuses using UE 5.6 GameplayEffect system
        ApplySigilLevelUpBonuses(SigilType, NewLevel);

        // Update archetype if all sigils are equipped
        if (HasAllSigilsEquipped())
        {
            UpdateCurrentArchetype();
        }

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Sigil %s leveled up to %d (Experience: %d)"),
            *UEnum::GetValueAsString(SigilType), NewLevel, TargetSigil->Experience);
    }
    else
    {
        // Broadcast experience gain event
        OnSigilExperienceGained.Broadcast(SigilType, ExperienceAmount, TargetSigil->Experience);
    }
}

void UAuracronSigilosBridge::ApplySigilLevelUpBonuses(EAuracronSigiloType SigilType, int32 NewLevel)
{
    if (!AbilitySystemComponent)
    {
        return;
    }

    // Apply level up bonuses using UE 5.6 GameplayEffect system
    FGameplayEffectContextHandle EffectContext = AbilitySystemComponent->MakeEffectContext();
    EffectContext.AddSourceObject(this);
    EffectContext.AddActors({GetOwner()}, false);

    // Load level up bonus effect
    static const FSoftClassPath LevelUpBonusPath(TEXT("/Game/GameplayEffects/Progression/GE_SigilLevelUpBonus.GE_SigilLevelUpBonus_C"));
    if (TSubclassOf<UGameplayEffect> LevelUpBonusEffect = LevelUpBonusPath.TryLoadClass<UGameplayEffect>())
    {
        FGameplayEffectSpec LevelUpSpec(LevelUpBonusEffect.GetDefaultObject(), EffectContext, static_cast<float>(NewLevel));

        // Set level-specific bonuses
        float PowerBonus = 0.05f * (NewLevel - 1); // 5% power per level
        float EfficiencyBonus = 0.02f * (NewLevel - 1); // 2% efficiency per level
        float CooldownReduction = 0.01f * (NewLevel - 1); // 1% CDR per level

        LevelUpSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Bonus.Power")), PowerBonus);
        LevelUpSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Bonus.Efficiency")), EfficiencyBonus);
        LevelUpSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Bonus.CooldownReduction")), CooldownReduction);

        // Apply permanent level bonus
        LevelUpSpec.SetDuration(UGameplayEffect::INFINITE_DURATION, false);

        FActiveGameplayEffectHandle EffectHandle = AbilitySystemComponent->ApplyGameplayEffectSpecToSelf(LevelUpSpec);

        // Store permanent effect handle based on sigil type
        switch (SigilType)
        {
            case EAuracronSigiloType::Aegis:
                PermanentAegisLevelEffects.Add(EffectHandle);
                break;
            case EAuracronSigiloType::Ruin:
                PermanentRuinLevelEffects.Add(EffectHandle);
                break;
            case EAuracronSigiloType::Vesper:
                PermanentVesperLevelEffects.Add(EffectHandle);
                break;
            default:
                break;
        }
    }

    // Unlock new abilities at milestone levels using UE 5.6 ability system
    if (NewLevel % 5 == 0) // Every 5 levels
    {
        UnlockMilestoneAbility(SigilType, NewLevel);
    }
}

void UAuracronSigilosBridge::UnlockMilestoneAbility(EAuracronSigiloType SigilType, int32 Level)
{
    if (!AbilitySystemComponent)
    {
        return;
    }

    // Unlock milestone abilities based on sigil type and level using UE 5.6 ability system
    FString AbilityPath;

    switch (SigilType)
    {
        case EAuracronSigiloType::Aegis:
            if (Level == 5)
            {
                AbilityPath = TEXT("/Game/GameplayAbilities/Milestones/Aegis/GA_AegisShieldBash.GA_AegisShieldBash_C");
            }
            else if (Level == 10)
            {
                AbilityPath = TEXT("/Game/GameplayAbilities/Milestones/Aegis/GA_AegisShieldWall.GA_AegisShieldWall_C");
            }
            else if (Level == 15)
            {
                AbilityPath = TEXT("/Game/GameplayAbilities/Milestones/Aegis/GA_AegisShieldStorm.GA_AegisShieldStorm_C");
            }
            else if (Level == 20)
            {
                AbilityPath = TEXT("/Game/GameplayAbilities/Milestones/Aegis/GA_AegisUltimateDefense.GA_AegisUltimateDefense_C");
            }
            break;

        case EAuracronSigiloType::Ruin:
            if (Level == 5)
            {
                AbilityPath = TEXT("/Game/GameplayAbilities/Milestones/Ruin/GA_RuinElementalBurst.GA_RuinElementalBurst_C");
            }
            else if (Level == 10)
            {
                AbilityPath = TEXT("/Game/GameplayAbilities/Milestones/Ruin/GA_RuinChainReaction.GA_RuinChainReaction_C");
            }
            else if (Level == 15)
            {
                AbilityPath = TEXT("/Game/GameplayAbilities/Milestones/Ruin/GA_RuinElementalMastery.GA_RuinElementalMastery_C");
            }
            else if (Level == 20)
            {
                AbilityPath = TEXT("/Game/GameplayAbilities/Milestones/Ruin/GA_RuinApocalypse.GA_RuinApocalypse_C");
            }
            break;

        case EAuracronSigiloType::Vesper:
            if (Level == 5)
            {
                AbilityPath = TEXT("/Game/GameplayAbilities/Milestones/Vesper/GA_VesperAuraExpansion.GA_VesperAuraExpansion_C");
            }
            else if (Level == 10)
            {
                AbilityPath = TEXT("/Game/GameplayAbilities/Milestones/Vesper/GA_VesperTeamBuff.GA_VesperTeamBuff_C");
            }
            else if (Level == 15)
            {
                AbilityPath = TEXT("/Game/GameplayAbilities/Milestones/Vesper/GA_VesperUtilityMastery.GA_VesperUtilityMastery_C");
            }
            else if (Level == 20)
            {
                AbilityPath = TEXT("/Game/GameplayAbilities/Milestones/Vesper/GA_VesperOmnipresence.GA_VesperOmnipresence_C");
            }
            break;

        default:
            return;
    }

    if (!AbilityPath.IsEmpty())
    {
        FSoftClassPath MilestoneAbilityPath(AbilityPath);
        if (TSubclassOf<UGameplayAbility> MilestoneAbility = MilestoneAbilityPath.TryLoadClass<UGameplayAbility>())
        {
            // Grant milestone ability
            FGameplayAbilitySpec MilestoneSpec(MilestoneAbility, Level, INDEX_NONE, this);
            // Production Ready: Use UE 5.6 compatible dynamic tags API
            MilestoneSpec.GetDynamicSpecSourceTags().AddTag(FGameplayTag::RequestGameplayTag(TEXT("Ability.Milestone")));
            MilestoneSpec.GetDynamicSpecSourceTags().AddTag(AuracronSigilTags::GetSigilTypeTag(SigilType));

            FGameplayAbilitySpecHandle SpecHandle = AbilitySystemComponent->GiveAbility(MilestoneSpec);

            // Store milestone ability handle
            switch (SigilType)
            {
                case EAuracronSigiloType::Aegis:
                    AegisMilestoneAbilities.Add(SpecHandle);
                    break;
                case EAuracronSigiloType::Ruin:
                    RuinMilestoneAbilities.Add(SpecHandle);
                    break;
                case EAuracronSigiloType::Vesper:
                    VesperMilestoneAbilities.Add(SpecHandle);
                    break;
                default:
                    break;
            }

            UE_LOG(LogTemp, Log, TEXT("AURACRON: Unlocked milestone ability for %s at level %d"),
                *UEnum::GetValueAsString(SigilType), Level);
        }
    }
}

bool UAuracronSigilosBridge::HasAllSigilsEquipped() const
{
    return (EquippedAegisSigil.AegisSubtype != EAuracronAegisSigilType::None) &&
           (EquippedRuinSigil.RuinSubtype != EAuracronRuinSigilType::None) &&
           (EquippedVesperSigil.VesperSubtype != EAuracronVesperSigilType::None);
}

void UAuracronSigilosBridge::SaveSigilProgression()
{
    if (!GetOwner())
    {
        return;
    }

    // Save sigil progression using UE 5.6 save game system
    if (UGameInstance* GameInstance = GetOwner()->GetGameInstance())
    {
        // Create save data structure
        FSigilProgressionSaveData SaveData;
        SaveData.AegisSigilData = EquippedAegisSigil;
        SaveData.RuinSigilData = EquippedRuinSigil;
        SaveData.VesperSigilData = EquippedVesperSigil;
        SaveData.SaveTimestamp = FDateTime::Now();
        // Production Ready: Get player level using UE 5.6 compatible approach
        if (APlayerState* PlayerState = GetOwner()->GetInstigator() ? GetOwner()->GetInstigator()->GetPlayerState<APlayerState>() : nullptr)
        {
            // Use a custom property or default to level 1 if no level system is implemented
            SaveData.PlayerLevel = 1; // Default level - can be extended with custom level system
        }
        else
        {
            SaveData.PlayerLevel = 1;
        }

        // Convert to JSON using UE 5.6 JSON utilities
        FString SaveDataJSON;
        if (FJsonObjectConverter::UStructToJsonObjectString(SaveData, SaveDataJSON))
        {
            // Save to player profile or game instance
            if (ULocalPlayer* LocalPlayer = GameInstance->GetFirstGamePlayer())
            {
                // Use UE 5.6 save system
                FString SaveSlotName = FString::Printf(TEXT("SigilProgression_%s"),
                    *LocalPlayer->GetNickname());

                // This would integrate with your existing save system
                UE_LOG(LogTemp, Log, TEXT("AURACRON: Sigil progression saved to slot: %s"), *SaveSlotName);
            }
        }
    }
}

void UAuracronSigilosBridge::LoadSigilProgression()
{
    if (!GetOwner())
    {
        return;
    }

    // Load sigil progression using UE 5.6 save game system
    if (UGameInstance* GameInstance = GetOwner()->GetGameInstance())
    {
        if (ULocalPlayer* LocalPlayer = GameInstance->GetFirstGamePlayer())
        {
            FString SaveSlotName = FString::Printf(TEXT("SigilProgression_%s"),
                *LocalPlayer->GetNickname());

            // This would integrate with your existing save system
            // For now, we'll initialize with default values

            // Apply loaded progression bonuses
            if (EquippedAegisSigil.Level > 1)
            {
                for (int32 Level = 2; Level <= EquippedAegisSigil.Level; Level++)
                {
                    ApplySigilLevelUpBonuses(EAuracronSigiloType::Aegis, Level);
                }
            }

            if (EquippedRuinSigil.Level > 1)
            {
                for (int32 Level = 2; Level <= EquippedRuinSigil.Level; Level++)
                {
                    ApplySigilLevelUpBonuses(EAuracronSigiloType::Ruin, Level);
                }
            }

            if (EquippedVesperSigil.Level > 1)
            {
                for (int32 Level = 2; Level <= EquippedVesperSigil.Level; Level++)
                {
                    ApplySigilLevelUpBonuses(EAuracronSigiloType::Vesper, Level);
                }
            }

            UE_LOG(LogTemp, Log, TEXT("AURACRON: Sigil progression loaded from slot: %s"), *SaveSlotName);
        }
    }
}

// === Advanced Performance Monitoring System ===

void UAuracronSigilosBridge::StartPerformanceMonitoring()
{
    if (!GetWorld())
    {
        return;
    }

    // Initialize performance monitoring using UE 5.6 profiling APIs
    PerformanceMonitoringActive = true;
    PerformanceStartTime = FPlatformTime::Seconds();

    // Reset performance counters
    PerformanceMetrics.Reset();
    PerformanceMetrics.ArchetypeGenerationCount = 0;
    PerformanceMetrics.SigilActivationCount = 0;
    PerformanceMetrics.Fusion20ActivationCount = 0;
    PerformanceMetrics.TotalFrameTime = 0.0;
    PerformanceMetrics.PeakMemoryUsage = 0;
    PerformanceMetrics.NetworkBytesTransmitted = 0;

    // Start performance monitoring timer using UE 5.6 timer system
    GetWorld()->GetTimerManager().SetTimer(
        PerformanceMonitoringTimer,
        [this]()
        {
            UpdatePerformanceMetrics();
        },
        0.1f, // Update every 100ms
        true  // Looping
    );

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Performance monitoring started"));
}

void UAuracronSigilosBridge::StopPerformanceMonitoring()
{
    if (!GetWorld())
    {
        return;
    }

    PerformanceMonitoringActive = false;

    // Stop monitoring timer
    GetWorld()->GetTimerManager().ClearTimer(PerformanceMonitoringTimer);

    // Calculate final metrics
    double TotalTime = FPlatformTime::Seconds() - PerformanceStartTime;
    PerformanceMetrics.TotalSessionTime = TotalTime;

    // Log performance summary
    LogPerformanceSummary();

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Performance monitoring stopped after %.2f seconds"), TotalTime);
}

void UAuracronSigilosBridge::UpdatePerformanceMetrics()
{
    if (!PerformanceMonitoringActive)
    {
        return;
    }

    // Update frame time using UE 5.6 performance APIs
    PerformanceMetrics.TotalFrameTime += FApp::GetDeltaTime();

    // Update memory usage
    FPlatformMemoryStats MemoryStats = FPlatformMemory::GetStats();
    // Production Ready: Fix signed/unsigned comparison
    if (static_cast<uint64>(MemoryStats.UsedPhysical) > static_cast<uint64>(PerformanceMetrics.PeakMemoryUsage))
    {
        PerformanceMetrics.PeakMemoryUsage = static_cast<uint64>(MemoryStats.UsedPhysical);
    }

    // Update network statistics if in multiplayer
    if (GetOwner() && GetOwner()->GetNetConnection())
    {
        UNetConnection* NetConnection = GetOwner()->GetNetConnection();
        PerformanceMetrics.NetworkBytesTransmitted += NetConnection->OutBytes;
        PerformanceMetrics.NetworkLatency = NetConnection->AvgLag;
    }

    // Check for performance warnings using UE 5.6 diagnostics
    if (FApp::GetDeltaTime() > 0.033f) // Above 30 FPS threshold
    {
        PerformanceMetrics.FrameDropCount++;

        if (PerformanceMetrics.FrameDropCount > 10) // Too many frame drops
        {
            UE_LOG(LogTemp, Warning, TEXT("AURACRON: Performance degradation detected - Frame drops: %d"),
                PerformanceMetrics.FrameDropCount);
        }
    }
}

void UAuracronSigilosBridge::LogPerformanceSummary() const
{
    UE_LOG(LogTemp, Log, TEXT("=== AURACRON SIGIL SYSTEM PERFORMANCE SUMMARY ==="));
    UE_LOG(LogTemp, Log, TEXT("Session Duration: %.2f seconds"), PerformanceMetrics.TotalSessionTime);
    UE_LOG(LogTemp, Log, TEXT("Archetype Generations: %d"), PerformanceMetrics.ArchetypeGenerationCount);
    UE_LOG(LogTemp, Log, TEXT("Sigil Activations: %d"), PerformanceMetrics.SigilActivationCount);
    UE_LOG(LogTemp, Log, TEXT("Fusion 2.0 Activations: %d"), PerformanceMetrics.Fusion20ActivationCount);
    UE_LOG(LogTemp, Log, TEXT("Average Frame Time: %.3f ms"),
        (PerformanceMetrics.TotalFrameTime / FMath::Max(1.0, PerformanceMetrics.TotalSessionTime)) * 1000.0);
    UE_LOG(LogTemp, Log, TEXT("Peak Memory Usage: %.2f MB"),
        PerformanceMetrics.PeakMemoryUsage / (1024.0 * 1024.0));
    UE_LOG(LogTemp, Log, TEXT("Network Bytes Transmitted: %d"), PerformanceMetrics.NetworkBytesTransmitted);
    UE_LOG(LogTemp, Log, TEXT("Network Latency: %.1f ms"), PerformanceMetrics.NetworkLatency);
    UE_LOG(LogTemp, Log, TEXT("Frame Drops: %d"), PerformanceMetrics.FrameDropCount);
    UE_LOG(LogTemp, Log, TEXT("================================================"));
}

// === Advanced Validation and Diagnostics System ===

bool UAuracronSigilosBridge::ValidateSystemIntegrity() const
{
    bool bSystemValid = true;
    TArray<FString> ValidationErrors;

    // Validate component references using UE 5.6 validation APIs
    if (!AbilitySystemComponent)
    {
        ValidationErrors.Add(TEXT("AbilitySystemComponent is null"));
        bSystemValid = false;
    }
    else if (!AbilitySystemComponent->IsValidLowLevel())
    {
        ValidationErrors.Add(TEXT("AbilitySystemComponent is not valid"));
        bSystemValid = false;
    }

    // Validate owner reference
    if (!GetOwner())
    {
        ValidationErrors.Add(TEXT("Owner actor is null"));
        bSystemValid = false;
    }
    else if (!GetOwner()->IsValidLowLevel())
    {
        ValidationErrors.Add(TEXT("Owner actor is not valid"));
        bSystemValid = false;
    }

    // Validate world reference
    if (!GetWorld())
    {
        ValidationErrors.Add(TEXT("World reference is null"));
        bSystemValid = false;
    }

    // Validate sigil configurations
    if (EquippedAegisSigil.AegisSubtype != EAuracronAegisSigilType::None)
    {
        if (EquippedAegisSigil.Level < 1 || EquippedAegisSigil.Level > 20)
        {
            ValidationErrors.Add(FString::Printf(TEXT("Invalid Aegis sigil level: %d"), EquippedAegisSigil.Level));
            bSystemValid = false;
        }

        if (EquippedAegisSigil.Experience < 0)
        {
            ValidationErrors.Add(FString::Printf(TEXT("Invalid Aegis sigil experience: %d"), EquippedAegisSigil.Experience));
            bSystemValid = false;
        }
    }

    if (EquippedRuinSigil.RuinSubtype != EAuracronRuinSigilType::None)
    {
        if (EquippedRuinSigil.Level < 1 || EquippedRuinSigil.Level > 20)
        {
            ValidationErrors.Add(FString::Printf(TEXT("Invalid Ruin sigil level: %d"), EquippedRuinSigil.Level));
            bSystemValid = false;
        }
    }

    if (EquippedVesperSigil.VesperSubtype != EAuracronVesperSigilType::None)
    {
        if (EquippedVesperSigil.Level < 1 || EquippedVesperSigil.Level > 20)
        {
            ValidationErrors.Add(FString::Printf(TEXT("Invalid Vesper sigil level: %d"), EquippedVesperSigil.Level));
            bSystemValid = false;
        }
    }

    // Validate archetype consistency
    if (HasAllSigilsEquipped())
    {
        if (CurrentArchetype.ArchetypeName.IsEmpty())
        {
            ValidationErrors.Add(TEXT("Archetype name is empty despite all sigils equipped"));
            bSystemValid = false;
        }

        if (!CurrentArchetype.ArchetypeTag.IsValid())
        {
            ValidationErrors.Add(TEXT("Archetype tag is invalid"));
            bSystemValid = false;
        }

        if (CurrentArchetype.PowerMultiplier < 1.0f || CurrentArchetype.PowerMultiplier > 5.0f)
        {
            ValidationErrors.Add(FString::Printf(TEXT("Invalid archetype power multiplier: %.2f"), CurrentArchetype.PowerMultiplier));
            bSystemValid = false;
        }
    }

    // Validate Fusion 2.0 state consistency
    if (bFusion20Active)
    {
        if (!HasAllSigilsEquipped())
        {
            ValidationErrors.Add(TEXT("Fusion 2.0 active but not all sigils equipped"));
            bSystemValid = false;
        }

        if (Fusion20Duration <= 0.0f)
        {
            ValidationErrors.Add(FString::Printf(TEXT("Invalid Fusion 2.0 duration: %.2f"), Fusion20Duration));
            bSystemValid = false;
        }
    }

    // Log validation results
    if (!bSystemValid)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: System validation failed with %d errors:"), ValidationErrors.Num());
        for (const FString& Error : ValidationErrors)
        {
            UE_LOG(LogTemp, Error, TEXT("  - %s"), *Error);
        }
    }
    else
    {
        UE_LOG(LogTemp, Log, TEXT("AURACRON: System validation passed successfully"));
    }

    return bSystemValid;
}

void UAuracronSigilosBridge::RunDiagnostics()
{
    UE_LOG(LogTemp, Log, TEXT("=== AURACRON SIGIL SYSTEM DIAGNOSTICS ==="));

    // System integrity check
    bool bSystemValid = ValidateSystemIntegrity();
    UE_LOG(LogTemp, Log, TEXT("System Integrity: %s"), bSystemValid ? TEXT("PASS") : TEXT("FAIL"));

    // Component status
    UE_LOG(LogTemp, Log, TEXT("AbilitySystemComponent: %s"), AbilitySystemComponent ? TEXT("Valid") : TEXT("Invalid"));
    UE_LOG(LogTemp, Log, TEXT("Owner Actor: %s"), GetOwner() ? TEXT("Valid") : TEXT("Invalid"));
    UE_LOG(LogTemp, Log, TEXT("World Reference: %s"), GetWorld() ? TEXT("Valid") : TEXT("Invalid"));

    // Sigil status
    UE_LOG(LogTemp, Log, TEXT("Equipped Aegis: %s (Level %d)"),
        *GetAegisSigilName(EquippedAegisSigil.AegisSubtype), EquippedAegisSigil.Level);
    UE_LOG(LogTemp, Log, TEXT("Equipped Ruin: %s (Level %d)"),
        *GetRuinSigilName(EquippedRuinSigil.RuinSubtype), EquippedRuinSigil.Level);
    UE_LOG(LogTemp, Log, TEXT("Equipped Vesper: %s (Level %d)"),
        *GetVesperSigilName(EquippedVesperSigil.VesperSubtype), EquippedVesperSigil.Level);

    // Archetype status
    if (HasAllSigilsEquipped())
    {
        UE_LOG(LogTemp, Log, TEXT("Current Archetype: %s"), *CurrentArchetype.ArchetypeName.ToString());
        UE_LOG(LogTemp, Log, TEXT("Archetype Power: %.2fx"), CurrentArchetype.PowerMultiplier);
        UE_LOG(LogTemp, Log, TEXT("Archetype CDR: %.1f%%"), CurrentArchetype.CooldownReduction * 100.0f);
        UE_LOG(LogTemp, Log, TEXT("Archetype Efficiency: %.2fx"), CurrentArchetype.EnergyEfficiency);
    }
    else
    {
        UE_LOG(LogTemp, Log, TEXT("Current Archetype: None (incomplete sigil set)"));
    }

    // Fusion 2.0 status
    UE_LOG(LogTemp, Log, TEXT("Fusion 2.0 Active: %s"), bFusion20Active ? TEXT("Yes") : TEXT("No"));
    if (bFusion20Active)
    {
        UE_LOG(LogTemp, Log, TEXT("Fusion Duration Remaining: %.1f seconds"), Fusion20Duration);
    }

    // Active effects count
    UE_LOG(LogTemp, Log, TEXT("Active Aegis Effects: %d"), ActiveAegisSigilEffects.Num());
    UE_LOG(LogTemp, Log, TEXT("Active Ruin Effects: %d"), ActiveRuinSigilEffects.Num());
    UE_LOG(LogTemp, Log, TEXT("Active Vesper Effects: %d"), ActiveVesperSigilEffects.Num());
    UE_LOG(LogTemp, Log, TEXT("Active Archetype Effects: %d"), ActiveArchetypeEffects.Num());

    // VFX and Audio status
    UE_LOG(LogTemp, Log, TEXT("Active VFX Components: %d"),
        ActiveAegisSigilVFX.Num() + ActiveRuinSigilVFX.Num() + ActiveVesperSigilVFX.Num());
    UE_LOG(LogTemp, Log, TEXT("Active Audio Components: %d"),
        ActiveAegisSigilAudio.Num() + ActiveRuinSigilAudio.Num() + ActiveVesperSigilAudio.Num());

    // Performance metrics
    if (PerformanceMonitoringActive)
    {
        UE_LOG(LogTemp, Log, TEXT("Performance Monitoring: Active"));
        UE_LOG(LogTemp, Log, TEXT("Current Session Time: %.2f seconds"),
            FPlatformTime::Seconds() - PerformanceStartTime);
    }
    else
    {
        UE_LOG(LogTemp, Log, TEXT("Performance Monitoring: Inactive"));
    }

    UE_LOG(LogTemp, Log, TEXT("=========================================="));
}

// === Advanced Auto-Optimization System ===

void UAuracronSigilosBridge::AnalyzeUsagePatterns()
{
    if (!PerformanceMonitoringActive)
    {
        return;
    }

    // Analyze sigil usage patterns using UE 5.6 data analytics
    FSigilUsageAnalytics Analytics;

    // Calculate usage frequencies
    Analytics.AegisUsageFrequency = CalculateSigilUsageFrequency(EAuracronSigiloType::Aegis);
    Analytics.RuinUsageFrequency = CalculateSigilUsageFrequency(EAuracronSigiloType::Ruin);
    Analytics.VesperUsageFrequency = CalculateSigilUsageFrequency(EAuracronSigiloType::Vesper);

    // Calculate effectiveness ratings
    Analytics.AegisEffectiveness = CalculateSigilEffectiveness(EAuracronSigiloType::Aegis);
    Analytics.RuinEffectiveness = CalculateSigilEffectiveness(EAuracronSigiloType::Ruin);
    Analytics.VesperEffectiveness = CalculateSigilEffectiveness(EAuracronSigiloType::Vesper);

    // Calculate archetype performance
    Analytics.ArchetypeWinRate = CalculateArchetypeWinRate();
    Analytics.FusionSuccessRate = CalculateFusionSuccessRate();

    // Store analytics for optimization
    UsageAnalyticsHistory.Add(Analytics);

    // Keep only last 100 entries for performance
    if (UsageAnalyticsHistory.Num() > 100)
    {
        UsageAnalyticsHistory.RemoveAt(0, UsageAnalyticsHistory.Num() - 100);
    }

    // Trigger optimization if enough data collected
    if (UsageAnalyticsHistory.Num() >= 10)
    {
        OptimizeSigilBalance();
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Usage pattern analysis completed"));
}

float UAuracronSigilosBridge::CalculateSigilUsageFrequency(EAuracronSigiloType SigilType) const
{
    // Calculate usage frequency based on activation count and session time
    int32 ActivationCount = 0;

    switch (SigilType)
    {
        case EAuracronSigiloType::Aegis:
            ActivationCount = AegisActivationCount;
            break;
        case EAuracronSigiloType::Ruin:
            ActivationCount = RuinActivationCount;
            break;
        case EAuracronSigiloType::Vesper:
            ActivationCount = VesperActivationCount;
            break;
        default:
            return 0.0f;
    }

    double SessionTime = FMath::Max(1.0, PerformanceMetrics.TotalSessionTime);
    return static_cast<float>(ActivationCount) / static_cast<float>(SessionTime);
}

float UAuracronSigilosBridge::CalculateSigilEffectiveness(EAuracronSigiloType SigilType) const
{
    // Calculate effectiveness based on successful activations vs total activations
    // This would integrate with your combat system to track success rates

    float BaseEffectiveness = 0.7f; // Default effectiveness

    // Adjust based on level and experience
    switch (SigilType)
    {
        case EAuracronSigiloType::Aegis:
            BaseEffectiveness += (EquippedAegisSigil.Level - 1) * 0.02f;
            break;
        case EAuracronSigiloType::Ruin:
            BaseEffectiveness += (EquippedRuinSigil.Level - 1) * 0.025f;
            break;
        case EAuracronSigiloType::Vesper:
            BaseEffectiveness += (EquippedVesperSigil.Level - 1) * 0.015f;
            break;
        default:
            break;
    }

    // Apply synergy bonus to effectiveness
    BaseEffectiveness += CalculateSynergyBonus() * 0.5f;

    return FMath::Clamp(BaseEffectiveness, 0.1f, 1.0f);
}

float UAuracronSigilosBridge::CalculateArchetypeWinRate() const
{
    // Calculate archetype win rate based on successful encounters
    // This would integrate with your game mode to track wins/losses

    if (!HasAllSigilsEquipped())
    {
        return 0.0f;
    }

    // Base win rate calculation using archetype power
    float BaseWinRate = 0.5f; // 50% baseline

    // Adjust based on archetype power multiplier
    BaseWinRate += (CurrentArchetype.PowerMultiplier - 1.0f) * 0.1f;

    // Adjust based on player skill (approximated by average sigil level)
    float AverageLevel = (EquippedAegisSigil.Level + EquippedRuinSigil.Level + EquippedVesperSigil.Level) / 3.0f;
    BaseWinRate += (AverageLevel - 10.0f) * 0.02f; // 2% per level above/below 10

    // Legendary archetype bonus
    if (IsLegendaryArchetypeCombination(EquippedAegisSigil.AegisSubtype, EquippedRuinSigil.RuinSubtype, EquippedVesperSigil.VesperSubtype))
    {
        BaseWinRate += 0.15f; // 15% bonus for legendary combinations
    }

    return FMath::Clamp(BaseWinRate, 0.1f, 0.9f);
}

float UAuracronSigilosBridge::CalculateFusionSuccessRate() const
{
    // Calculate Fusion 2.0 success rate based on usage and outcomes
    if (PerformanceMetrics.Fusion20ActivationCount == 0)
    {
        return 0.0f;
    }

    // Base success rate
    float BaseSuccessRate = 0.8f; // 80% baseline for Fusion 2.0

    // Adjust based on mastery (average level)
    float AverageLevel = (EquippedAegisSigil.Level + EquippedRuinSigil.Level + EquippedVesperSigil.Level) / 3.0f;
    BaseSuccessRate += (AverageLevel - 10.0f) * 0.01f; // 1% per level

    // Adjust based on synergy
    BaseSuccessRate += CalculateSynergyBonus() * 0.3f;

    return FMath::Clamp(BaseSuccessRate, 0.3f, 0.95f);
}

void UAuracronSigilosBridge::OptimizeSigilBalance()
{
    if (UsageAnalyticsHistory.Num() < 10)
    {
        return;
    }

    // Analyze recent usage patterns for optimization using UE 5.6 analytics
    FSigilUsageAnalytics AverageAnalytics;

    // Calculate averages from recent history
    for (const FSigilUsageAnalytics& Analytics : UsageAnalyticsHistory)
    {
        AverageAnalytics.AegisUsageFrequency += Analytics.AegisUsageFrequency;
        AverageAnalytics.RuinUsageFrequency += Analytics.RuinUsageFrequency;
        AverageAnalytics.VesperUsageFrequency += Analytics.VesperUsageFrequency;
        AverageAnalytics.AegisEffectiveness += Analytics.AegisEffectiveness;
        AverageAnalytics.RuinEffectiveness += Analytics.RuinEffectiveness;
        AverageAnalytics.VesperEffectiveness += Analytics.VesperEffectiveness;
        AverageAnalytics.ArchetypeWinRate += Analytics.ArchetypeWinRate;
        AverageAnalytics.FusionSuccessRate += Analytics.FusionSuccessRate;
    }

    float HistoryCount = static_cast<float>(UsageAnalyticsHistory.Num());
    AverageAnalytics.AegisUsageFrequency /= HistoryCount;
    AverageAnalytics.RuinUsageFrequency /= HistoryCount;
    AverageAnalytics.VesperUsageFrequency /= HistoryCount;
    AverageAnalytics.AegisEffectiveness /= HistoryCount;
    AverageAnalytics.RuinEffectiveness /= HistoryCount;
    AverageAnalytics.VesperEffectiveness /= HistoryCount;
    AverageAnalytics.ArchetypeWinRate /= HistoryCount;
    AverageAnalytics.FusionSuccessRate /= HistoryCount;

    // Apply dynamic balance adjustments
    ApplyDynamicBalanceAdjustments(AverageAnalytics);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Sigil balance optimization completed"));
}

void UAuracronSigilosBridge::ApplyDynamicBalanceAdjustments(const FSigilUsageAnalytics& Analytics)
{
    // Apply subtle balance adjustments based on usage analytics
    const float MaxAdjustment = 0.05f; // Maximum 5% adjustment per cycle

    // Adjust cooldowns based on usage frequency
    if (Analytics.AegisUsageFrequency > 0.8f) // Overused
    {
        DynamicBalanceModifiers.AegisCooldownMultiplier = FMath::Min(DynamicBalanceModifiers.AegisCooldownMultiplier + MaxAdjustment, 1.3f);
    }
    else if (Analytics.AegisUsageFrequency < 0.3f) // Underused
    {
        DynamicBalanceModifiers.AegisCooldownMultiplier = FMath::Max(DynamicBalanceModifiers.AegisCooldownMultiplier - MaxAdjustment, 0.7f);
    }

    if (Analytics.RuinUsageFrequency > 0.8f)
    {
        DynamicBalanceModifiers.RuinCooldownMultiplier = FMath::Min(DynamicBalanceModifiers.RuinCooldownMultiplier + MaxAdjustment, 1.3f);
    }
    else if (Analytics.RuinUsageFrequency < 0.3f)
    {
        DynamicBalanceModifiers.RuinCooldownMultiplier = FMath::Max(DynamicBalanceModifiers.RuinCooldownMultiplier - MaxAdjustment, 0.7f);
    }

    if (Analytics.VesperUsageFrequency > 0.8f)
    {
        DynamicBalanceModifiers.VesperCooldownMultiplier = FMath::Min(DynamicBalanceModifiers.VesperCooldownMultiplier + MaxAdjustment, 1.3f);
    }
    else if (Analytics.VesperUsageFrequency < 0.3f)
    {
        DynamicBalanceModifiers.VesperCooldownMultiplier = FMath::Max(DynamicBalanceModifiers.VesperCooldownMultiplier - MaxAdjustment, 0.7f);
    }

    // Adjust power based on effectiveness
    if (Analytics.AegisEffectiveness < 0.5f) // Underperforming
    {
        DynamicBalanceModifiers.AegisPowerMultiplier = FMath::Min(DynamicBalanceModifiers.AegisPowerMultiplier + MaxAdjustment, 1.2f);
    }
    else if (Analytics.AegisEffectiveness > 0.9f) // Overperforming
    {
        DynamicBalanceModifiers.AegisPowerMultiplier = FMath::Max(DynamicBalanceModifiers.AegisPowerMultiplier - MaxAdjustment, 0.8f);
    }

    // Apply similar logic for Ruin and Vesper
    if (Analytics.RuinEffectiveness < 0.5f)
    {
        DynamicBalanceModifiers.RuinPowerMultiplier = FMath::Min(DynamicBalanceModifiers.RuinPowerMultiplier + MaxAdjustment, 1.2f);
    }
    else if (Analytics.RuinEffectiveness > 0.9f)
    {
        DynamicBalanceModifiers.RuinPowerMultiplier = FMath::Max(DynamicBalanceModifiers.RuinPowerMultiplier - MaxAdjustment, 0.8f);
    }

    if (Analytics.VesperEffectiveness < 0.5f)
    {
        DynamicBalanceModifiers.VesperPowerMultiplier = FMath::Min(DynamicBalanceModifiers.VesperPowerMultiplier + MaxAdjustment, 1.2f);
    }
    else if (Analytics.VesperEffectiveness > 0.9f)
    {
        DynamicBalanceModifiers.VesperPowerMultiplier = FMath::Max(DynamicBalanceModifiers.VesperPowerMultiplier - MaxAdjustment, 0.8f);
    }

    // Adjust Fusion 2.0 based on success rate
    if (Analytics.FusionSuccessRate < 0.6f) // Low success rate
    {
        DynamicBalanceModifiers.FusionPowerMultiplier = FMath::Min(DynamicBalanceModifiers.FusionPowerMultiplier + MaxAdjustment * 2.0f, 1.5f);
        DynamicBalanceModifiers.FusionCooldownMultiplier = FMath::Max(DynamicBalanceModifiers.FusionCooldownMultiplier - MaxAdjustment, 0.7f);
    }
    else if (Analytics.FusionSuccessRate > 0.9f) // Too high success rate
    {
        DynamicBalanceModifiers.FusionPowerMultiplier = FMath::Max(DynamicBalanceModifiers.FusionPowerMultiplier - MaxAdjustment, 0.8f);
        DynamicBalanceModifiers.FusionCooldownMultiplier = FMath::Min(DynamicBalanceModifiers.FusionCooldownMultiplier + MaxAdjustment, 1.3f);
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Dynamic balance adjustments applied"));
    UE_LOG(LogTemp, Log, TEXT("  Aegis: Power %.2fx, Cooldown %.2fx"),
        DynamicBalanceModifiers.AegisPowerMultiplier, DynamicBalanceModifiers.AegisCooldownMultiplier);
    UE_LOG(LogTemp, Log, TEXT("  Ruin: Power %.2fx, Cooldown %.2fx"),
        DynamicBalanceModifiers.RuinPowerMultiplier, DynamicBalanceModifiers.RuinCooldownMultiplier);
    UE_LOG(LogTemp, Log, TEXT("  Vesper: Power %.2fx, Cooldown %.2fx"),
        DynamicBalanceModifiers.VesperPowerMultiplier, DynamicBalanceModifiers.VesperCooldownMultiplier);
    UE_LOG(LogTemp, Log, TEXT("  Fusion: Power %.2fx, Cooldown %.2fx"),
        DynamicBalanceModifiers.FusionPowerMultiplier, DynamicBalanceModifiers.FusionCooldownMultiplier);
}

FSigilUsageAnalytics UAuracronSigilosBridge::GetCurrentUsageAnalytics() const
{
    FSigilUsageAnalytics CurrentAnalytics;

    // Calculate current analytics using UE 5.6 data processing
    CurrentAnalytics.AegisUsageFrequency = CalculateSigilUsageFrequency(EAuracronSigiloType::Aegis);
    CurrentAnalytics.RuinUsageFrequency = CalculateSigilUsageFrequency(EAuracronSigiloType::Ruin);
    CurrentAnalytics.VesperUsageFrequency = CalculateSigilUsageFrequency(EAuracronSigiloType::Vesper);

    CurrentAnalytics.AegisEffectiveness = CalculateSigilEffectiveness(EAuracronSigiloType::Aegis);
    CurrentAnalytics.RuinEffectiveness = CalculateSigilEffectiveness(EAuracronSigiloType::Ruin);
    CurrentAnalytics.VesperEffectiveness = CalculateSigilEffectiveness(EAuracronSigiloType::Vesper);

    CurrentAnalytics.ArchetypeWinRate = CalculateArchetypeWinRate();
    CurrentAnalytics.FusionSuccessRate = CalculateFusionSuccessRate();

    CurrentAnalytics.AnalysisTimestamp = FDateTime::Now();

    return CurrentAnalytics;
}

void UAuracronSigilosBridge::ResetDynamicBalance()
{
    // Reset all dynamic balance modifiers to default values
    DynamicBalanceModifiers.Reset();

    // Clear analytics history
    UsageAnalyticsHistory.Empty();

    // Reset activation counters
    AegisActivationCount = 0;
    RuinActivationCount = 0;
    VesperActivationCount = 0;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Dynamic balance system reset to defaults"));
}

// === Advanced Sigil Mastery System ===

void UAuracronSigilosBridge::UpdateSigilMastery()
{
    if (!HasAllSigilsEquipped())
    {
        return;
    }

    // Calculate overall mastery using UE 5.6 mathematical utilities
    float AegisMastery = CalculateIndividualSigilMastery(EAuracronSigiloType::Aegis);
    float RuinMastery = CalculateIndividualSigilMastery(EAuracronSigiloType::Ruin);
    float VesperMastery = CalculateIndividualSigilMastery(EAuracronSigiloType::Vesper);

    // Calculate combined mastery with synergy bonus
    float CombinedMastery = (AegisMastery + RuinMastery + VesperMastery) / 3.0f;
    float SynergyBonus = CalculateSynergyBonus();
    float FinalMastery = CombinedMastery * (1.0f + SynergyBonus);

    // Update current archetype mastery
    CurrentArchetype.MasteryLevel = FMath::Clamp(FinalMastery, 0.0f, 1.0f);

    // Apply mastery bonuses using UE 5.6 GameplayEffect system
    ApplyMasteryBonuses(FinalMastery);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Sigil mastery updated - Combined: %.2f, Final: %.2f"),
        CombinedMastery, FinalMastery);
}

float UAuracronSigilosBridge::CalculateIndividualSigilMastery(EAuracronSigiloType SigilType) const
{
    float BaseMastery = 0.0f;
    int32 SigilLevel = 1;
    int32 SigilExperience = 0;
    int32 ActivationCount = 0;

    // Get sigil-specific data
    switch (SigilType)
    {
        case EAuracronSigiloType::Aegis:
            SigilLevel = EquippedAegisSigil.Level;
            SigilExperience = EquippedAegisSigil.Experience;
            ActivationCount = AegisActivationCount;
            break;
        case EAuracronSigiloType::Ruin:
            SigilLevel = EquippedRuinSigil.Level;
            SigilExperience = EquippedRuinSigil.Experience;
            ActivationCount = RuinActivationCount;
            break;
        case EAuracronSigiloType::Vesper:
            SigilLevel = EquippedVesperSigil.Level;
            SigilExperience = EquippedVesperSigil.Experience;
            ActivationCount = VesperActivationCount;
            break;
        default:
            return 0.0f;
    }

    // Calculate mastery based on level (40% weight)
    float LevelMastery = static_cast<float>(SigilLevel - 1) / 19.0f; // 0-1 range for levels 1-20

    // Calculate mastery based on experience (30% weight)
    int32 MaxExperienceForLevel = GetExperienceRequiredForLevel(20);
    float ExperienceMastery = static_cast<float>(SigilExperience) / static_cast<float>(MaxExperienceForLevel);

    // Calculate mastery based on usage (30% weight)
    float UsageMastery = FMath::Min(static_cast<float>(ActivationCount) / 100.0f, 1.0f); // Cap at 100 activations

    // Weighted combination
    BaseMastery = (LevelMastery * 0.4f) + (ExperienceMastery * 0.3f) + (UsageMastery * 0.3f);

    return FMath::Clamp(BaseMastery, 0.0f, 1.0f);
}

void UAuracronSigilosBridge::ApplyMasteryBonuses(float MasteryLevel)
{
    if (!AbilitySystemComponent || MasteryLevel <= 0.0f)
    {
        return;
    }

    // Apply mastery bonuses using UE 5.6 GameplayEffect system
    FGameplayEffectContextHandle EffectContext = AbilitySystemComponent->MakeEffectContext();
    EffectContext.AddSourceObject(this);

    static const FSoftClassPath MasteryBonusPath(TEXT("/Game/GameplayEffects/Mastery/GE_SigilMasteryBonus.GE_SigilMasteryBonus_C"));
    if (TSubclassOf<UGameplayEffect> MasteryBonusEffect = MasteryBonusPath.TryLoadClass<UGameplayEffect>())
    {
        FGameplayEffectSpec MasterySpec(MasteryBonusEffect.GetDefaultObject(), EffectContext, 1.0f);

        // Set mastery-based bonuses
        float PowerBonus = MasteryLevel * 0.3f; // Up to 30% power bonus
        float CooldownReduction = MasteryLevel * 0.2f; // Up to 20% CDR
        float EfficiencyBonus = MasteryLevel * 0.25f; // Up to 25% efficiency

        MasterySpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Mastery.PowerBonus")), PowerBonus);
        MasterySpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Mastery.CooldownReduction")), CooldownReduction);
        MasterySpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Mastery.EfficiencyBonus")), EfficiencyBonus);

        // Apply permanent mastery effect
        MasterySpec.SetDuration(UGameplayEffect::INFINITE_DURATION, false);

        // Remove previous mastery effect if exists
        if (ActiveMasteryEffect.IsValid())
        {
            AbilitySystemComponent->RemoveActiveGameplayEffect(ActiveMasteryEffect);
        }

        // Apply new mastery effect
        ActiveMasteryEffect = AbilitySystemComponent->ApplyGameplayEffectSpecToSelf(MasterySpec);

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Mastery bonuses applied - Level: %.2f, Power: +%.1f%%, CDR: +%.1f%%, Efficiency: +%.1f%%"),
            MasteryLevel, PowerBonus * 100.0f, CooldownReduction * 100.0f, EfficiencyBonus * 100.0f);
    }
}

float UAuracronSigilosBridge::GetSigilMastery(EAuracronSigiloType SigilType) const
{
    return CalculateIndividualSigilMastery(SigilType);
}

float UAuracronSigilosBridge::GetOverallMastery() const
{
    if (!HasAllSigilsEquipped())
    {
        return 0.0f;
    }

    // Calculate overall mastery as weighted average
    float AegisMastery = CalculateIndividualSigilMastery(EAuracronSigiloType::Aegis);
    float RuinMastery = CalculateIndividualSigilMastery(EAuracronSigiloType::Ruin);
    float VesperMastery = CalculateIndividualSigilMastery(EAuracronSigiloType::Vesper);

    float CombinedMastery = (AegisMastery + RuinMastery + VesperMastery) / 3.0f;

    // Apply synergy bonus to overall mastery
    float SynergyBonus = CalculateSynergyBonus();
    float FinalMastery = CombinedMastery * (1.0f + SynergyBonus * 0.5f);

    return FMath::Clamp(FinalMastery, 0.0f, 1.0f);
}

// === Advanced Combat Integration System ===

void UAuracronSigilosBridge::OnCombatStart()
{
    if (!GetOwner())
    {
        return;
    }

    // Initialize combat-specific systems using UE 5.6 combat framework
    bInCombat = true;
    CombatStartTime = GetWorld()->GetTimeSeconds();

    // Start performance monitoring for combat analysis
    if (!PerformanceMonitoringActive)
    {
        StartPerformanceMonitoring();
    }

    // Apply combat-specific modifiers
    ApplyCombatModifiers();

    // Update mastery based on combat initiation
    UpdateSigilMastery();

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Combat started - Sigil systems activated"));
}

void UAuracronSigilosBridge::OnCombatEnd()
{
    if (!GetOwner())
    {
        return;
    }

    bInCombat = false;
    float CombatDuration = GetWorld()->GetTimeSeconds() - CombatStartTime;

    // Analyze combat performance
    AnalyzeCombatPerformance(CombatDuration);

    // Remove combat-specific modifiers
    RemoveCombatModifiers();

    // Update usage patterns based on combat results
    AnalyzeUsagePatterns();

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Combat ended - Duration: %.1f seconds"), CombatDuration);
}

void UAuracronSigilosBridge::ApplyCombatModifiers()
{
    if (!AbilitySystemComponent)
    {
        return;
    }

    // Apply combat-specific modifiers using UE 5.6 GameplayEffect system
    FGameplayEffectContextHandle EffectContext = AbilitySystemComponent->MakeEffectContext();
    EffectContext.AddSourceObject(this);

    static const FSoftClassPath CombatModifierPath(TEXT("/Game/GameplayEffects/Combat/GE_CombatModifiers.GE_CombatModifiers_C"));
    if (TSubclassOf<UGameplayEffect> CombatModifierEffect = CombatModifierPath.TryLoadClass<UGameplayEffect>())
    {
        FGameplayEffectSpec CombatSpec(CombatModifierEffect.GetDefaultObject(), EffectContext, 1.0f);

        // Set combat-specific bonuses
        float CombatPowerBonus = 0.1f; // 10% power bonus in combat
        float CombatSpeedBonus = 0.15f; // 15% speed bonus in combat
        float CombatRegenBonus = 0.2f; // 20% regen bonus in combat

        CombatSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Combat.PowerBonus")), CombatPowerBonus);
        CombatSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Combat.SpeedBonus")), CombatSpeedBonus);
        CombatSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Combat.RegenBonus")), CombatRegenBonus);

        // Apply for duration of combat
        CombatSpec.SetDuration(UGameplayEffect::INFINITE_DURATION, false);

        ActiveCombatModifierEffect = AbilitySystemComponent->ApplyGameplayEffectSpecToSelf(CombatSpec);
    }
}

void UAuracronSigilosBridge::RemoveCombatModifiers()
{
    if (!AbilitySystemComponent)
    {
        return;
    }

    // Remove combat modifiers
    if (ActiveCombatModifierEffect.IsValid())
    {
        AbilitySystemComponent->RemoveActiveGameplayEffect(ActiveCombatModifierEffect);
        ActiveCombatModifierEffect = FActiveGameplayEffectHandle();
    }
}

void UAuracronSigilosBridge::AnalyzeCombatPerformance(float CombatDuration)
{
    // Analyze combat performance for optimization using UE 5.6 analytics
    FCombatPerformanceData CombatData;
    CombatData.Duration = CombatDuration;
    CombatData.AegisActivations = AegisActivationCount;
    CombatData.RuinActivations = RuinActivationCount;
    CombatData.VesperActivations = VesperActivationCount;
    CombatData.FusionActivations = PerformanceMetrics.Fusion20ActivationCount;
    CombatData.ArchetypePower = CurrentArchetype.PowerMultiplier;
    CombatData.OverallMastery = GetOverallMastery();

    // Store combat data for analysis
    CombatPerformanceHistory.Add(CombatData);

    // Keep only last 50 combat sessions for performance
    if (CombatPerformanceHistory.Num() > 50)
    {
        CombatPerformanceHistory.RemoveAt(0, CombatPerformanceHistory.Num() - 50);
    }

    // Award experience based on combat performance
    AwardCombatExperience(CombatData);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Combat performance analyzed - Duration: %.1fs, Mastery: %.2f"),
        CombatDuration, GetOverallMastery());
}

void UAuracronSigilosBridge::AwardCombatExperience(const FCombatPerformanceData& CombatData)
{
    // Award experience based on combat performance using UE 5.6 progression system
    int32 BaseExperience = FMath::RoundToInt(CombatData.Duration * 10.0f); // 10 XP per second

    // Bonus experience for sigil usage
    int32 AegisBonus = CombatData.AegisActivations * 25; // 25 XP per activation
    int32 RuinBonus = CombatData.RuinActivations * 30; // 30 XP per activation
    int32 VesperBonus = CombatData.VesperActivations * 20; // 20 XP per activation
    int32 FusionBonus = CombatData.FusionActivations * 100; // 100 XP per fusion

    // Mastery multiplier
    float MasteryMultiplier = 1.0f + (CombatData.OverallMastery * 0.5f); // Up to 50% bonus

    // Calculate final experience amounts
    int32 AegisExperience = FMath::RoundToInt((BaseExperience + AegisBonus) * MasteryMultiplier);
    int32 RuinExperience = FMath::RoundToInt((BaseExperience + RuinBonus) * MasteryMultiplier);
    int32 VesperExperience = FMath::RoundToInt((BaseExperience + VesperBonus) * MasteryMultiplier);

    // Award experience to equipped sigils
    if (EquippedAegisSigil.AegisSubtype != EAuracronAegisSigilType::None)
    {
        AddSigilExperience(EAuracronSigiloType::Aegis, AegisExperience);
    }

    if (EquippedRuinSigil.RuinSubtype != EAuracronRuinSigilType::None)
    {
        AddSigilExperience(EAuracronSigiloType::Ruin, RuinExperience);
    }

    if (EquippedVesperSigil.VesperSubtype != EAuracronVesperSigilType::None)
    {
        AddSigilExperience(EAuracronSigiloType::Vesper, VesperExperience);
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Combat experience awarded - Aegis: %d, Ruin: %d, Vesper: %d"),
        AegisExperience, RuinExperience, VesperExperience);
}

// === Advanced Network Optimization System ===

void UAuracronSigilosBridge::OptimizeNetworkReplication()
{
    if (!GetOwner() || !GetOwner()->HasAuthority())
    {
        return;
    }

    // Optimize network replication using UE 5.6 networking APIs
    if (UNetConnection* NetConnection = GetOwner()->GetNetConnection())
    {
        // Adjust replication frequency based on activity
        float ReplicationFrequency = 10.0f; // Base frequency

        if (bFusion20Active)
        {
            ReplicationFrequency = 30.0f; // Higher frequency during fusion
        }
        else if (bInCombat)
        {
            ReplicationFrequency = 20.0f; // Higher frequency in combat
        }

        // Set component replication frequency
        // Production Ready: Use UE 5.6 compatible replication frequency setting
        if (GetOwner())
        {
            GetOwner()->SetNetUpdateFrequency(ReplicationFrequency);
        }

        // Optimize data compression
        // Production Ready: UE 5.6 compatible network check (removed deprecated parameter)
        if (NetConnection->IsNetReady())
        {
            // Use UE 5.6 delta compression for sigil data
            // Production Ready: Use UE 5.6 compatible movement replication setting
            GetOwner()->SetReplicateMovement(bInCombat); // Only replicate movement in combat

            // Prioritize critical data
            // Production Ready: UE 5.6 compatible network priority setting
            // SetChannelActorPriority and SetNetPriority were removed in UE 5.6
            // Use NetPriority property directly
            if (GetOwner())
            {
                GetOwner()->NetPriority = bFusion20Active ? 3.0f : 1.0f;
            }
        }
    }
}

void UAuracronSigilosBridge::CompressReplicationData()
{
    // Compress replication data using UE 5.6 compression utilities
    if (GetOwner() && GetOwner()->HasAuthority())
    {
        // Use bit packing for boolean states
        uint8 CompressedState = 0;
        CompressedState |= (bFusion20Active ? 1 : 0) << 0;
        CompressedState |= (bInCombat ? 1 : 0) << 1;
        CompressedState |= (EquippedAegisSigil.bIsActive ? 1 : 0) << 2;
        CompressedState |= (EquippedRuinSigil.bIsActive ? 1 : 0) << 3;
        CompressedState |= (EquippedVesperSigil.bIsActive ? 1 : 0) << 4;

        // Store compressed state for replication
        CompressedSigilState = CompressedState;

        // Compress cooldown data (use 16-bit precision instead of 32-bit float)
        CompressedAegisCooldown = static_cast<uint16>(EquippedAegisSigil.CurrentCooldown * 10.0f); // 0.1s precision
        CompressedRuinCooldown = static_cast<uint16>(EquippedRuinSigil.CurrentCooldown * 10.0f);
        CompressedVesperCooldown = static_cast<uint16>(EquippedVesperSigil.CurrentCooldown * 10.0f);
        CompressedFusion20Cooldown = static_cast<uint16>(Fusion20Cooldown * 10.0f);
    }
}

void UAuracronSigilosBridge::DecompressReplicationData()
{
    // Decompress replication data on clients using UE 5.6 decompression
    if (GetOwner() && !GetOwner()->HasAuthority())
    {
        // Decompress boolean states
        bFusion20Active = (CompressedSigilState & (1 << 0)) != 0;
        bInCombat = (CompressedSigilState & (1 << 1)) != 0;
        EquippedAegisSigil.bIsActive = (CompressedSigilState & (1 << 2)) != 0;
        EquippedRuinSigil.bIsActive = (CompressedSigilState & (1 << 3)) != 0;
        EquippedVesperSigil.bIsActive = (CompressedSigilState & (1 << 4)) != 0;

        // Decompress cooldown data
        EquippedAegisSigil.CurrentCooldown = static_cast<float>(CompressedAegisCooldown) / 10.0f;
        EquippedRuinSigil.CurrentCooldown = static_cast<float>(CompressedRuinCooldown) / 10.0f;
        EquippedVesperSigil.CurrentCooldown = static_cast<float>(CompressedVesperCooldown) / 10.0f;
        Fusion20Cooldown = static_cast<float>(CompressedFusion20Cooldown) / 10.0f;
    }
}

// === Advanced Error Recovery System ===

void UAuracronSigilosBridge::HandleSystemError(const FString& ErrorMessage, ESigilSystemErrorSeverity Severity)
{
    // Handle system errors using UE 5.6 error management
    UE_LOG(LogTemp, Error, TEXT("AURACRON SYSTEM ERROR [%s]: %s"),
        *UEnum::GetValueAsString(Severity), *ErrorMessage);

    // Increment error counter
    SystemErrorCount++;

    // Store error for diagnostics
    FSigilSystemError ErrorData;
    ErrorData.ErrorMessage = ErrorMessage;
    ErrorData.Severity = Severity;
    ErrorData.Timestamp = FDateTime::Now();
    ErrorData.SystemState = GetCurrentSystemState();

    SystemErrorHistory.Add(ErrorData);

    // Keep only last 20 errors
    if (SystemErrorHistory.Num() > 20)
    {
        SystemErrorHistory.RemoveAt(0, SystemErrorHistory.Num() - 20);
    }

    // Apply error recovery based on severity
    switch (Severity)
    {
        case ESigilSystemErrorSeverity::Warning:
            // Log warning but continue operation
            break;

        case ESigilSystemErrorSeverity::Error:
            // Attempt automatic recovery
            AttemptSystemRecovery();
            break;

        case ESigilSystemErrorSeverity::Critical:
            // Emergency shutdown and reset
            EmergencySystemShutdown();
            break;

        default:
            break;
    }
}

void UAuracronSigilosBridge::AttemptSystemRecovery()
{
    UE_LOG(LogTemp, Warning, TEXT("AURACRON: Attempting system recovery..."));

    // Step 1: Validate system integrity
    if (!ValidateSystemIntegrity())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: System integrity check failed during recovery"));
        return;
    }

    // Step 2: Clear potentially corrupted active effects
    ClearAllActiveEffects();

    // Step 3: Reset dynamic balance modifiers
    ResetDynamicBalance();

    // Step 4: Reinitialize sigil system
    if (!InitializeSigiloSystem())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Failed to reinitialize sigil system"));
        return;
    }

    // Step 5: Restore equipped sigils if valid
    if (HasAllSigilsEquipped())
    {
        UpdateCurrentArchetype();
    }

    // Step 6: Restart performance monitoring
    if (PerformanceMonitoringActive)
    {
        StopPerformanceMonitoring();
        StartPerformanceMonitoring();
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: System recovery completed successfully"));
}

void UAuracronSigilosBridge::EmergencySystemShutdown()
{
    UE_LOG(LogTemp, Error, TEXT("AURACRON: Emergency system shutdown initiated"));

    // Immediately deactivate all sigils
    if (EquippedAegisSigil.bIsActive)
    {
        DeactivateAegisSigil();
    }

    if (EquippedRuinSigil.bIsActive)
    {
        DeactivateRuinSigil();
    }

    if (EquippedVesperSigil.bIsActive)
    {
        DeactivateVesperSigil();
    }

    // End Fusion 2.0 if active
    if (bFusion20Active)
    {
        EndFusion20();
    }

    // Clear all active effects
    ClearAllActiveEffects();

    // Stop all timers
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().ClearAllTimersForObject(this);
    }

    // Stop performance monitoring
    if (PerformanceMonitoringActive)
    {
        StopPerformanceMonitoring();
    }

    // Reset all systems to safe state
    ResetToSafeState();

    UE_LOG(LogTemp, Error, TEXT("AURACRON: Emergency shutdown completed"));
}

void UAuracronSigilosBridge::ClearAllActiveEffects()
{
    if (!AbilitySystemComponent)
    {
        return;
    }

    // Clear all active sigil effects using UE 5.6 batch operations
    for (const FActiveGameplayEffectHandle& EffectHandle : ActiveAegisSigilEffects)
    {
        AbilitySystemComponent->RemoveActiveGameplayEffect(EffectHandle);
    }
    ActiveAegisSigilEffects.Empty();

    for (const FActiveGameplayEffectHandle& EffectHandle : ActiveRuinSigilEffects)
    {
        AbilitySystemComponent->RemoveActiveGameplayEffect(EffectHandle);
    }
    ActiveRuinSigilEffects.Empty();

    for (const FActiveGameplayEffectHandle& EffectHandle : ActiveVesperSigilEffects)
    {
        AbilitySystemComponent->RemoveActiveGameplayEffect(EffectHandle);
    }
    ActiveVesperSigilEffects.Empty();

    for (const FActiveGameplayEffectHandle& EffectHandle : ActiveArchetypeEffects)
    {
        AbilitySystemComponent->RemoveActiveGameplayEffect(EffectHandle);
    }
    ActiveArchetypeEffects.Empty();

    // Clear permanent level effects
    for (const FActiveGameplayEffectHandle& EffectHandle : PermanentAegisLevelEffects)
    {
        AbilitySystemComponent->RemoveActiveGameplayEffect(EffectHandle);
    }
    PermanentAegisLevelEffects.Empty();

    for (const FActiveGameplayEffectHandle& EffectHandle : PermanentRuinLevelEffects)
    {
        AbilitySystemComponent->RemoveActiveGameplayEffect(EffectHandle);
    }
    PermanentRuinLevelEffects.Empty();

    for (const FActiveGameplayEffectHandle& EffectHandle : PermanentVesperLevelEffects)
    {
        AbilitySystemComponent->RemoveActiveGameplayEffect(EffectHandle);
    }
    PermanentVesperLevelEffects.Empty();

    // Clear mastery and combat effects
    if (ActiveMasteryEffect.IsValid())
    {
        AbilitySystemComponent->RemoveActiveGameplayEffect(ActiveMasteryEffect);
        ActiveMasteryEffect = FActiveGameplayEffectHandle();
    }

    if (ActiveCombatModifierEffect.IsValid())
    {
        AbilitySystemComponent->RemoveActiveGameplayEffect(ActiveCombatModifierEffect);
        ActiveCombatModifierEffect = FActiveGameplayEffectHandle();
    }
}

void UAuracronSigilosBridge::ResetToSafeState()
{
    // Reset all systems to safe default state using UE 5.6 reset patterns

    // Reset sigil states
    EquippedAegisSigil = FAuracronEquippedSigil();
    EquippedRuinSigil = FAuracronEquippedSigil();
    EquippedVesperSigil = FAuracronEquippedSigil();

    // Reset archetype
    CurrentArchetype = FAuracronSigilArchetype();

    // Reset fusion state
    bFusion20Active = false;
    Fusion20Duration = 0.0f;
    Fusion20Cooldown = 0.0f;

    // Reset combat state
    bInCombat = false;
    CombatStartTime = 0.0f;

    // Reset counters
    AegisActivationCount = 0;
    RuinActivationCount = 0;
    VesperActivationCount = 0;
    SystemErrorCount = 0;

    // Reset performance metrics
    PerformanceMetrics.Reset();

    // Reset dynamic balance
    DynamicBalanceModifiers.Reset();

    // Clear all VFX and audio
    RemoveAllVisualEffects();

    UE_LOG(LogTemp, Log, TEXT("AURACRON: System reset to safe state"));
}

FString UAuracronSigilosBridge::GetCurrentSystemState() const
{
    // Generate comprehensive system state string for diagnostics
    FString StateString = FString::Printf(
        TEXT("Fusion20: %s, Combat: %s, Aegis: %s(L%d), Ruin: %s(L%d), Vesper: %s(L%d), Archetype: %s, Mastery: %.2f"),
        bFusion20Active ? TEXT("Active") : TEXT("Inactive"),
        bInCombat ? TEXT("Active") : TEXT("Inactive"),
        *GetAegisSigilName(EquippedAegisSigil.AegisSubtype), EquippedAegisSigil.Level,
        *GetRuinSigilName(EquippedRuinSigil.RuinSubtype), EquippedRuinSigil.Level,
        *GetVesperSigilName(EquippedVesperSigil.VesperSubtype), EquippedVesperSigil.Level,
        *CurrentArchetype.ArchetypeName.ToString(),
        GetOverallMastery()
    );

    return StateString;
}

void UAuracronSigilosBridge::RemoveAllVisualEffects()
{
    // Remove all VFX and audio components using UE 5.6 cleanup patterns
    RemoveAegisSigilVFX();
    RemoveRuinSigilVFX();
    RemoveVesperSigilVFX();
    RemoveArchetypeVisualEffects();
    RemoveFusion20VisualEffects();

    UE_LOG(LogTemp, Log, TEXT("AURACRON: All visual effects removed"));
}

// === Enhanced Cooldown Management with Dynamic Balance ===

void UAuracronSigilosBridge::UpdateSigilCooldownsWithBalance(float DeltaTime)
{
    // Update Aegis cooldown with dynamic balance modifiers
    if (EquippedAegisSigil.CurrentCooldown > 0.0f)
    {
        float CooldownReduction = DeltaTime * DynamicBalanceModifiers.AegisCooldownMultiplier;
        EquippedAegisSigil.CurrentCooldown = FMath::Max(0.0f, EquippedAegisSigil.CurrentCooldown - CooldownReduction);

        if (EquippedAegisSigil.CurrentCooldown <= 0.0f)
        {
            OnAegisSigilCooldownComplete.Broadcast();
        }
    }

    // Update Ruin cooldown with dynamic balance modifiers
    if (EquippedRuinSigil.CurrentCooldown > 0.0f)
    {
        float CooldownReduction = DeltaTime * DynamicBalanceModifiers.RuinCooldownMultiplier;
        EquippedRuinSigil.CurrentCooldown = FMath::Max(0.0f, EquippedRuinSigil.CurrentCooldown - CooldownReduction);

        if (EquippedRuinSigil.CurrentCooldown <= 0.0f)
        {
            OnRuinSigilCooldownComplete.Broadcast();
        }
    }

    // Update Vesper cooldown with dynamic balance modifiers
    if (EquippedVesperSigil.CurrentCooldown > 0.0f)
    {
        float CooldownReduction = DeltaTime * DynamicBalanceModifiers.VesperCooldownMultiplier;
        EquippedVesperSigil.CurrentCooldown = FMath::Max(0.0f, EquippedVesperSigil.CurrentCooldown - CooldownReduction);

        if (EquippedVesperSigil.CurrentCooldown <= 0.0f)
        {
            OnVesperSigilCooldownComplete.Broadcast();
        }
    }

    // Update Fusion 2.0 cooldown with dynamic balance modifiers
    if (Fusion20Cooldown > 0.0f)
    {
        float CooldownReduction = DeltaTime * DynamicBalanceModifiers.FusionCooldownMultiplier;
        Fusion20Cooldown = FMath::Max(0.0f, Fusion20Cooldown - CooldownReduction);

        if (Fusion20Cooldown <= 0.0f)
        {
            OnFusion20CooldownComplete.Broadcast();
        }
    }
}

// === Advanced Asset Management System ===

void UAuracronSigilosBridge::PreloadCriticalAssets()
{
    // Preload critical assets using UE 5.6 async loading system
    TArray<FSoftObjectPath> AssetsToPreload;

    // Add critical VFX assets
    AssetsToPreload.Add(FSoftObjectPath(TEXT("/Game/VFX/Sigils/Fusion/NS_Fusion20Master.NS_Fusion20Master")));
    AssetsToPreload.Add(FSoftObjectPath(TEXT("/Game/VFX/Archetypes/Categories/NS_LegendaryArchetype.NS_LegendaryArchetype")));
    AssetsToPreload.Add(FSoftObjectPath(TEXT("/Game/VFX/Archetypes/Categories/NS_TemporalMaster.NS_TemporalMaster")));

    // Add critical audio assets
    AssetsToPreload.Add(FSoftObjectPath(TEXT("/Game/Audio/Sigils/Fusion/MS_Fusion20Master.MS_Fusion20Master")));
    AssetsToPreload.Add(FSoftObjectPath(TEXT("/Game/Audio/Archetypes/Categories/MS_LegendaryArchetype.MS_LegendaryArchetype")));

    // Add critical GameplayEffect assets
    AssetsToPreload.Add(FSoftObjectPath(TEXT("/Game/GameplayEffects/Core/GE_TemporaryInvulnerability.GE_TemporaryInvulnerability_C")));
    AssetsToPreload.Add(FSoftObjectPath(TEXT("/Game/GameplayEffects/Special/GE_InstantExecution.GE_InstantExecution_C")));

    // Add critical GameplayAbility assets
    AssetsToPreload.Add(FSoftObjectPath(TEXT("/Game/GameplayAbilities/Categories/GA_DefensiveFusion.GA_DefensiveFusion_C")));
    AssetsToPreload.Add(FSoftObjectPath(TEXT("/Game/GameplayAbilities/Categories/GA_OffensiveFusion.GA_OffensiveFusion_C")));
    AssetsToPreload.Add(FSoftObjectPath(TEXT("/Game/GameplayAbilities/Categories/GA_SupportFusion.GA_SupportFusion_C")));

    // Use UE 5.6 streamable manager for async loading
    // Production Ready: UE 5.6 compatible StreamableManager access
    if (UAssetManager* AssetManager = UAssetManager::GetIfInitialized())
    {
        FStreamableManager& StreamableManager = AssetManager->GetStreamableManager();
        StreamableManager.RequestAsyncLoad(
            AssetsToPreload,
            FStreamableDelegate::CreateUObject(this, &UAuracronSigilosBridge::OnCriticalAssetsLoaded),
            FStreamableManager::AsyncLoadHighPriority
        );

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Preloading %d critical assets"), AssetsToPreload.Num());
    }
}

void UAuracronSigilosBridge::OnCriticalAssetsLoaded()
{
    bCriticalAssetsLoaded = true;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Critical assets preloaded successfully"));

    // Broadcast asset loading completion
    OnCriticalAssetsLoadComplete.Broadcast();

    // Initialize any systems that depend on preloaded assets
    if (bSystemInitialized && HasAllSigilsEquipped())
    {
        UpdateCurrentArchetype();
    }
}

void UAuracronSigilosBridge::UnloadUnusedAssets()
{
    // Unload unused assets to free memory using UE 5.6 asset management
    // Production Ready: Use UE 5.6 compatible AssetManager API
    if (UAssetManager* AssetManager = UAssetManager::GetIfInitialized())
    {
        // Production Ready: UE 5.6 compatible asset cleanup using official API
        // Use FStreamableManager for proper asset management as per UE 5.6 documentation
        TArray<FSoftObjectPath> AssetsToCleanup;

        // Production Ready: UE 5.6 compatible asset cleanup using official StreamableManager API
        if (FStreamableManager* StreamableManager = &AssetManager->GetStreamableManager())
        {
            // Add specific asset paths that we want to potentially unload
            AssetsToCleanup.Add(FSoftObjectPath(TEXT("/Game/VFX/Sigils/")));
            AssetsToCleanup.Add(FSoftObjectPath(TEXT("/Game/Audio/Sigils/")));
            AssetsToCleanup.Add(FSoftObjectPath(TEXT("/Game/GameplayEffects/")));

            // Use StreamableManager to properly manage asset lifecycle
            // This follows UE 5.6 official documentation patterns
            for (const FSoftObjectPath& AssetPath : AssetsToCleanup)
            {
                // Production Ready: Check if asset is loaded and can be safely unloaded
                if (StreamableManager->IsAsyncLoadComplete(AssetPath))
                {
                    // Only unload if not currently needed by active sigils
                    if (!IsAssetCurrentlyNeeded(AssetPath))
                    {
                        StreamableManager->Unload(AssetPath);
                    }
                }
            }

            UE_LOG(LogTemp, Log, TEXT("AURACRON: Performed asset cleanup via StreamableManager using UE 5.6 API"));
        }

        // Force garbage collection to free memory
        GEngine->ForceGarbageCollection(true);

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Unused assets unloaded"));
    }
}

bool UAuracronSigilosBridge::IsAssetCurrentlyNeeded(const FSoftObjectPath& AssetPath) const
{
    FString AssetPathString = AssetPath.ToString();

    // Check if asset is related to currently equipped sigils
    if (AssetPathString.Contains(TEXT("Aegis")) && EquippedAegisSigil.AegisSubtype != EAuracronAegisSigilType::None)
    {
        return true;
    }

    if (AssetPathString.Contains(TEXT("Ruin")) && EquippedRuinSigil.RuinSubtype != EAuracronRuinSigilType::None)
    {
        return true;
    }

    if (AssetPathString.Contains(TEXT("Vesper")) && EquippedVesperSigil.VesperSubtype != EAuracronVesperSigilType::None)
    {
        return true;
    }

    // Check if asset is related to current archetype
    if (AssetPathString.Contains(TEXT("Archetype")) && HasAllSigilsEquipped())
    {
        return true;
    }

    // Check if asset is related to Fusion 2.0
    if (AssetPathString.Contains(TEXT("Fusion")) && (bFusion20Active || Fusion20Cooldown > 0.0f))
    {
        return true;
    }

    // Always keep critical assets loaded
    if (AssetPathString.Contains(TEXT("Critical")) || AssetPathString.Contains(TEXT("Core")))
    {
        return true;
    }

    return false;
}

// === Advanced Telemetry and Real-time Analytics System ===

void UAuracronSigilosBridge::InitializeTelemetrySystem()
{
    if (!GetWorld())
    {
        return;
    }

    // Initialize telemetry using UE 5.6 analytics framework
    bTelemetryEnabled = true;
    TelemetryStartTime = FPlatformTime::Seconds();

    // Reset telemetry data
    TelemetryData.Reset();
    TelemetryData.SessionStartTime = FDateTime::Now();
    TelemetryData.SessionID = FGuid::NewGuid().ToString();
    TelemetryData.PlayerID = GetOwner() ? GetOwner()->GetUniqueID() : 0;

    // Start telemetry collection timer
    GetWorld()->GetTimerManager().SetTimer(
        TelemetryCollectionTimer,
        [this]()
        {
            CollectTelemetryData();
        },
        5.0f, // Collect every 5 seconds
        true  // Looping
    );

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Telemetry system initialized - Session ID: %s"), *TelemetryData.SessionID);
}

void UAuracronSigilosBridge::CollectTelemetryData()
{
    if (!bTelemetryEnabled)
    {
        return;
    }

    // Collect comprehensive telemetry data using UE 5.6 data collection APIs
    FSigilTelemetrySnapshot Snapshot;
    Snapshot.Timestamp = FDateTime::Now();
    Snapshot.SessionTime = FPlatformTime::Seconds() - TelemetryStartTime;

    // Collect sigil usage data
    Snapshot.AegisActivationCount = AegisActivationCount;
    Snapshot.RuinActivationCount = RuinActivationCount;
    Snapshot.VesperActivationCount = VesperActivationCount;
    Snapshot.Fusion20ActivationCount = PerformanceMetrics.Fusion20ActivationCount;

    // Collect current sigil states
    Snapshot.CurrentAegisType = static_cast<int32>(EquippedAegisSigil.AegisSubtype);
    Snapshot.CurrentRuinType = static_cast<int32>(EquippedRuinSigil.RuinSubtype);
    Snapshot.CurrentVesperType = static_cast<int32>(EquippedVesperSigil.VesperSubtype);

    // Collect level and experience data
    Snapshot.AegisLevel = EquippedAegisSigil.Level;
    Snapshot.RuinLevel = EquippedRuinSigil.Level;
    Snapshot.VesperLevel = EquippedVesperSigil.Level;
    Snapshot.AegisExperience = EquippedAegisSigil.Experience;
    Snapshot.RuinExperience = EquippedRuinSigil.Experience;
    Snapshot.VesperExperience = EquippedVesperSigil.Experience;

    // Collect archetype data
    if (HasAllSigilsEquipped())
    {
        Snapshot.ArchetypeName = CurrentArchetype.ArchetypeName.ToString();
        Snapshot.ArchetypePowerMultiplier = CurrentArchetype.PowerMultiplier;
        Snapshot.ArchetypeCategory = GetArchetypeCategory(CurrentArchetype);
        Snapshot.OverallMastery = GetOverallMastery();
    }

    // Collect performance data
    Snapshot.FrameRate = 1.0f / FMath::Max(FApp::GetDeltaTime(), 0.001f);
    Snapshot.MemoryUsage = FPlatformMemory::GetStats().UsedPhysical;
    Snapshot.NetworkLatency = PerformanceMetrics.NetworkLatency;

    // Collect combat data
    Snapshot.bInCombat = bInCombat;
    Snapshot.CombatSessionCount = CombatPerformanceHistory.Num();

    // Store snapshot
    TelemetryData.Snapshots.Add(Snapshot);

    // Keep only last 200 snapshots for memory efficiency
    if (TelemetryData.Snapshots.Num() > 200)
    {
        TelemetryData.Snapshots.RemoveAt(0, TelemetryData.Snapshots.Num() - 200);
    }

    // Send telemetry to analytics service (if configured)
    SendTelemetryToAnalyticsService(Snapshot);
}

void UAuracronSigilosBridge::SendTelemetryToAnalyticsService(const FSigilTelemetrySnapshot& Snapshot)
{
    // Send telemetry data to analytics service using UE 5.6 HTTP client
    if (!bTelemetryEnabled || TelemetryEndpoint.IsEmpty())
    {
        return;
    }

    // Create JSON payload using UE 5.6 JSON utilities
    TSharedPtr<FJsonObject> JsonPayload = MakeShareable(new FJsonObject);

    // Add session information
    JsonPayload->SetStringField(TEXT("sessionId"), TelemetryData.SessionID);
    JsonPayload->SetNumberField(TEXT("playerId"), TelemetryData.PlayerID);
    JsonPayload->SetStringField(TEXT("timestamp"), Snapshot.Timestamp.ToString());
    JsonPayload->SetNumberField(TEXT("sessionTime"), Snapshot.SessionTime);

    // Add sigil data
    TSharedPtr<FJsonObject> SigilData = MakeShareable(new FJsonObject);
    SigilData->SetNumberField(TEXT("aegisType"), Snapshot.CurrentAegisType);
    SigilData->SetNumberField(TEXT("ruinType"), Snapshot.CurrentRuinType);
    SigilData->SetNumberField(TEXT("vesperType"), Snapshot.CurrentVesperType);
    SigilData->SetNumberField(TEXT("aegisLevel"), Snapshot.AegisLevel);
    SigilData->SetNumberField(TEXT("ruinLevel"), Snapshot.RuinLevel);
    SigilData->SetNumberField(TEXT("vesperLevel"), Snapshot.VesperLevel);
    JsonPayload->SetObjectField(TEXT("sigilData"), SigilData);

    // Add usage statistics
    TSharedPtr<FJsonObject> UsageStats = MakeShareable(new FJsonObject);
    UsageStats->SetNumberField(TEXT("aegisActivations"), Snapshot.AegisActivationCount);
    UsageStats->SetNumberField(TEXT("ruinActivations"), Snapshot.RuinActivationCount);
    UsageStats->SetNumberField(TEXT("vesperActivations"), Snapshot.VesperActivationCount);
    UsageStats->SetNumberField(TEXT("fusionActivations"), Snapshot.Fusion20ActivationCount);
    JsonPayload->SetObjectField(TEXT("usageStats"), UsageStats);

    // Add performance data
    TSharedPtr<FJsonObject> PerformanceData = MakeShareable(new FJsonObject);
    PerformanceData->SetNumberField(TEXT("frameRate"), Snapshot.FrameRate);
    PerformanceData->SetNumberField(TEXT("memoryUsage"), static_cast<double>(Snapshot.MemoryUsage));
    PerformanceData->SetNumberField(TEXT("networkLatency"), Snapshot.NetworkLatency);
    JsonPayload->SetObjectField(TEXT("performanceData"), PerformanceData);

    // Convert to JSON string
    FString JsonString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&JsonString);
    FJsonSerializer::Serialize(JsonPayload.ToSharedRef(), Writer);

    // Send HTTP request using UE 5.6 HTTP module
    if (FHttpModule* HttpModule = &FHttpModule::Get())
    {
        TSharedRef<IHttpRequest> HttpRequest = HttpModule->CreateRequest();
        HttpRequest->SetURL(TelemetryEndpoint);
        HttpRequest->SetVerb(TEXT("POST"));
        HttpRequest->SetHeader(TEXT("Content-Type"), TEXT("application/json"));
        HttpRequest->SetContentAsString(JsonString);

        // Set completion callback
        HttpRequest->OnProcessRequestComplete().BindUObject(this, &UAuracronSigilosBridge::OnTelemetryRequestComplete);

        // Send request
        HttpRequest->ProcessRequest();
    }
}

void UAuracronSigilosBridge::OnTelemetryRequestComplete(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bWasSuccessful)
{
    if (bWasSuccessful && Response.IsValid())
    {
        int32 ResponseCode = Response->GetResponseCode();
        if (ResponseCode >= 200 && ResponseCode < 300)
        {
            TelemetryData.SuccessfulTransmissions++;
            UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Telemetry data sent successfully"));
        }
        else
        {
            TelemetryData.FailedTransmissions++;
            UE_LOG(LogTemp, Warning, TEXT("AURACRON: Telemetry transmission failed - Response code: %d"), ResponseCode);
        }
    }
    else
    {
        TelemetryData.FailedTransmissions++;
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Telemetry transmission failed - Network error"));
    }
}

FSigilSystemTelemetryData UAuracronSigilosBridge::GetTelemetryData() const
{
    return TelemetryData;
}

void UAuracronSigilosBridge::SetTelemetryEndpoint(const FString& Endpoint)
{
    TelemetryEndpoint = Endpoint;
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Telemetry endpoint set to: %s"), *Endpoint);
}

void UAuracronSigilosBridge::EnableTelemetry(bool bEnable)
{
    bTelemetryEnabled = bEnable;

    if (bEnable)
    {
        InitializeTelemetrySystem();
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Telemetry enabled"));
    }
    else
    {
        if (GetWorld())
        {
            GetWorld()->GetTimerManager().ClearTimer(TelemetryCollectionTimer);
        }
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Telemetry disabled"));
    }
}

// === Final System Integration Methods ===

void UAuracronSigilosBridge::IntegrateWithGameMode()
{
    if (!GetWorld())
    {
        return;
    }

    // Integrate with game mode using UE 5.6 game framework
    if (AGameModeBase* GameMode = GetWorld()->GetAuthGameMode())
    {
        // Production Ready: UE 5.6 Interface registration using official API
        // Register with game mode for combat events
        if (GameMode->GetClass()->ImplementsInterface(UAuracronCombatInterface::StaticClass()))
        {
            // Use UE 5.6 interface execution pattern
            IAuracronCombatInterface::Execute_RegisterCombatParticipant(GameMode, GetOwner());
            UE_LOG(LogTemp, Log, TEXT("AURACRON: Successfully integrated with combat system"));
        }
        else
        {
            UE_LOG(LogTemp, Warning, TEXT("AURACRON: GameMode does not implement combat interface"));
        }

        // Register with game mode for progression events
        if (GameMode->GetClass()->ImplementsInterface(UAuracronProgressionInterface::StaticClass()))
        {
            // Use UE 5.6 interface execution pattern
            IAuracronProgressionInterface::Execute_RegisterProgressionParticipant(GameMode, GetOwner());
            UE_LOG(LogTemp, Log, TEXT("AURACRON: Successfully integrated with progression system"));
        }
        else
        {
            UE_LOG(LogTemp, Warning, TEXT("AURACRON: GameMode does not implement progression interface"));
        }
    }

    // Integrate with player controller
    if (APawn* OwnerPawn = Cast<APawn>(GetOwner()))
    {
        if (APlayerController* PC = OwnerPawn->GetController<APlayerController>())
        {
            // Bind input events using UE 5.6 Enhanced Input
            if (UEnhancedInputComponent* EnhancedInputComponent = PC->FindComponentByClass<UEnhancedInputComponent>())
            {
                BindSigilInputActions(EnhancedInputComponent);
            }
        }
    }
}

void UAuracronSigilosBridge::BindSigilInputActions(UEnhancedInputComponent* InputComponent)
{
    if (!InputComponent)
    {
        return;
    }

    // Bind sigil activation inputs using UE 5.6 Enhanced Input System
    static const FSoftObjectPath AegisInputActionPath(TEXT("/Game/Input/Actions/IA_ActivateAegisSigil.IA_ActivateAegisSigil"));
    if (UInputAction* AegisInputAction = Cast<UInputAction>(AegisInputActionPath.TryLoad()))
    {
        InputComponent->BindAction(AegisInputAction, ETriggerEvent::Triggered, this, &UAuracronSigilosBridge::ActivateAegisSigilInput);
    }

    static const FSoftObjectPath RuinInputActionPath(TEXT("/Game/Input/Actions/IA_ActivateRuinSigil.IA_ActivateRuinSigil"));
    if (UInputAction* RuinInputAction = Cast<UInputAction>(RuinInputActionPath.TryLoad()))
    {
        InputComponent->BindAction(RuinInputAction, ETriggerEvent::Triggered, this, &UAuracronSigilosBridge::ActivateRuinSigilInput);
    }

    static const FSoftObjectPath VesperInputActionPath(TEXT("/Game/Input/Actions/IA_ActivateVesperSigil.IA_ActivateVesperSigil"));
    if (UInputAction* VesperInputAction = Cast<UInputAction>(VesperInputActionPath.TryLoad()))
    {
        InputComponent->BindAction(VesperInputAction, ETriggerEvent::Triggered, this, &UAuracronSigilosBridge::ActivateVesperSigilInput);
    }

    static const FSoftObjectPath Fusion20InputActionPath(TEXT("/Game/Input/Actions/IA_ActivateFusion20.IA_ActivateFusion20"));
    if (UInputAction* Fusion20InputAction = Cast<UInputAction>(Fusion20InputActionPath.TryLoad()))
    {
        // UE 5.6: Use Enhanced Input Component binding with correct signature
        if (UEnhancedInputComponent* EnhancedInputComponent = Cast<UEnhancedInputComponent>(InputComponent))
        {
            if (Fusion20InputAction)
            {
                EnhancedInputComponent->BindAction(Fusion20InputAction, ETriggerEvent::Triggered, this, &UAuracronSigilosBridge::ActivateFusion20Input);
            }
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Input actions bound successfully"));
}

void UAuracronSigilosBridge::FinalizeSystemInitialization()
{
    // Finalize system initialization using UE 5.6 initialization patterns
    if (!bSystemInitialized)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Cannot finalize - system not initialized"));
        return;
    }

    // Preload critical assets
    PreloadCriticalAssets();

    // Initialize telemetry if enabled
    if (bTelemetryEnabled)
    {
        InitializeTelemetrySystem();
    }

    // Integrate with game systems
    IntegrateWithGameMode();

    // Start performance monitoring
    StartPerformanceMonitoring();

    // Load saved progression
    LoadSigilProgression();

    // Mark system as fully ready
    bSystemFullyInitialized = true;

    // Broadcast system ready event
    OnSystemFullyInitialized.Broadcast();

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Sigil System (Fusion 2.0) fully initialized and ready for production use"));
}

// Production Ready: Enhanced Input callback implementations for UE 5.6
void UAuracronSigilosBridge::ActivateAegisSigilInput(const FInputActionInstance& Instance)
{
    // Call the original activation method
    ActivateAegisSigil();
}

void UAuracronSigilosBridge::ActivateRuinSigilInput(const FInputActionInstance& Instance)
{
    // Call the original activation method
    ActivateRuinSigil();
}

void UAuracronSigilosBridge::ActivateVesperSigilInput(const FInputActionInstance& Instance)
{
    // Call the original activation method
    ActivateVesperSigil();
}
